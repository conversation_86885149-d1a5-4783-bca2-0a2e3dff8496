using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Events;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads.Events;

/// <summary>
/// Event handler for ValidationCompletedEvent that manages validation error persistence asynchronously
/// </summary>
public class ValidationCompletedEventHandler(
    IValidationErrorManagerService validationErrorManager,
    ILogger<ValidationCompletedEventHandler> logger) : INotificationHandler<ValidationCompletedEvent>
{
    public async Task Handle(ValidationCompletedEvent notification, CancellationToken cancellationToken)
    {
        logger.LogDebug("Handling validation completed event for upload {UploadId}", notification.AggregateId);

        try
        {
            // Clear any previous validation errors for this upload
            await validationErrorManager.ClearPreviousValidationErrorsAsync(notification.AggregateId, cancellationToken);

            // Persist new validation errors from the event data
            await validationErrorManager.PersistValidationErrorsAsync(
                notification.AggregateId, notification.MemberErrors, cancellationToken);

            logger.LogInformation("Completed validation error persistence for upload {UploadId}. Valid: {ValidCount}, Invalid: {InvalidCount}",
                notification.AggregateId, notification.ValidCount, notification.InvalidCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to handle validation completed event for upload {UploadId}", notification.AggregateId);
            throw;
        }
    }
}
