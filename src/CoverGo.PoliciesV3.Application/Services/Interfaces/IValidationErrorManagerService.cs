using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

/// <summary>
/// Service for managing validation errors in an event-driven approach
/// </summary>
public interface IValidationErrorManagerService
{
    /// <summary>
    /// Clears all previous validation errors for the specified upload
    /// </summary>
    /// <param name="uploadId">The upload ID to clear errors for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task ClearPreviousValidationErrorsAsync(PolicyMemberUploadId uploadId, CancellationToken cancellationToken);

    /// <summary>
    /// Persists validation errors from the validation results
    /// </summary>
    /// <param name="uploadId">The upload ID</param>
    /// <param name="memberErrors">Dictionary mapping member indices to their validation errors</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task PersistValidationErrorsAsync(
        PolicyMemberUploadId uploadId, 
        Dictionary<int, List<ValidationError>> memberErrors, 
        CancellationToken cancellationToken);
}
