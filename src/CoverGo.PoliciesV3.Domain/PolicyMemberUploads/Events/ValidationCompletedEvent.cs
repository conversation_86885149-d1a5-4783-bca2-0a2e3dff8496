using CoverGo.PoliciesV3.Domain.Common;
using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Events;

/// <summary>
/// Domain event raised when validation is completed for a policy member upload
/// </summary>
public class ValidationCompletedEvent : DomainEvent<PolicyMemberUploadId>
{
    public int ValidCount { get; init; }
    public int InvalidCount { get; init; }
    public Dictionary<int, List<ValidationError>> MemberErrors { get; init; }

    public ValidationCompletedEvent(
        PolicyMemberUploadId uploadId,
        int validCount,
        int invalidCount,
        Dictionary<int, List<ValidationError>> memberErrors) : base(uploadId)
    {
        ValidCount = validCount;
        InvalidCount = invalidCount;
        MemberErrors = memberErrors;
    }
}
