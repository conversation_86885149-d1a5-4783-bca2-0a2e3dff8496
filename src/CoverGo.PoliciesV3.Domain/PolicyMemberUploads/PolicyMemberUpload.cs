using System.ComponentModel.DataAnnotations;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Events;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

/// <summary>
/// Represents a batch upload of policy members, tracking the file, member counts, and the status of the upload process
/// </summary>
public class PolicyMemberUpload : AggregateRootBase<PolicyMemberUploadId>
{
    #region Constructors

    private PolicyMemberUpload(PolicyMemberUploadId id) : base(id)
    {
    }

    public PolicyMemberUpload() : this(PolicyMemberUploadId.New)
    {
    }

    #endregion

    #region Fields / Properties

    // Entity Relationships
    /// <summary>
    /// Foreign key to the Policy
    /// </summary>
    public PolicyId PolicyId { get; private set; } = PolicyId.Empty;

    /// <summary>
    /// Optional foreign key to an Endorsement
    /// </summary>
    public EndorsementId? EndorsementId { get; private set; }

    // File Information
    /// <summary>
    /// Path to the uploaded file
    /// </summary>
    public string Path { get; private set; } = string.Empty;

    // Member Counts
    /// <summary>
    /// Total number of members in the uploaded file
    /// </summary>
    public int MembersCount { get; private set; }

    /// <summary>
    /// Number of valid members after validation
    /// </summary>
    public int? ValidMembersCount { get; private set; }

    /// <summary>
    /// Number of invalid members after validation
    /// </summary>
    public int? InvalidMembersCount { get; private set; }

    // Status Information
    /// <summary>
    /// Current status of the upload process
    /// </summary>
    public PolicyMemberUploadStatus Status { get; private set; } = PolicyMemberUploadStatus.REGISTERED;

    private void SetStatus(PolicyMemberUploadStatus value) => Status = value;

    // Audit tracking is handled by EntityAuditInfo (IAuditableEntity)

    // Collections
    /// <summary>
    /// Collection of validation errors
    /// </summary>
    public ICollection<PolicyMemberUploadValidationError> ValidationErrors { get; set; } = [];

    /// <summary>
    /// Collection of import results
    /// </summary>
    public ICollection<PolicyMemberUploadImportedResult> ImportedResults { get; set; } = [];

    // Concurrency Control - maps to PostgreSQL xmin system column
    /// <summary>
    /// Row version for optimistic concurrency control
    /// </summary>
    [Timestamp]
    public uint RowVersion { get; set; }

    #endregion

    #region Factory Methods

    /// <summary>
    /// Creates a new policy member upload
    /// </summary>
    /// <param name="policyId">The policy this upload belongs to</param>
    /// <param name="path">Path to the uploaded file</param>
    /// <param name="membersCount">Total number of members in the file</param>
    /// <param name="endorsementId">Optional endorsement ID</param>
    /// <returns>A new PolicyMemberUpload instance</returns>
    public static PolicyMemberUpload Create(
        PolicyId policyId,
        string path,
        int membersCount,
        EndorsementId? endorsementId = null)
    {
        var upload = new PolicyMemberUpload
        {
            PolicyId = policyId,
            Path = path,
            MembersCount = membersCount,
            EndorsementId = endorsementId,
            Status = PolicyMemberUploadStatus.REGISTERED
        };
        return upload;
    }

    #endregion

    #region Validation Process

    /// <summary>
    /// Ensures the upload can be validated (more flexible than StartValidating)
    /// Allows validation from REGISTERED, VALIDATING_ERROR, VALIDATED, or FAILED states
    /// </summary>
    /// <exception cref="InvalidOperationException">Thrown when validation cannot proceed</exception>
    public void EnsureCanValidate() => EnsureStatusIs(PolicyMemberUploadStatus.REGISTERED, PolicyMemberUploadStatus.VALIDATING_ERROR,
                      PolicyMemberUploadStatus.VALIDATED, PolicyMemberUploadStatus.FAILED);

    /// <summary>
    /// Sets the validation counts and attempts to finish validation
    /// </summary>
    /// <param name="validCount">Number of valid members</param>
    /// <param name="invalidCount">Number of invalid members</param>
    private void SetValidationCounts(int validCount, int invalidCount)
    {
        ValidMembersCount = validCount;
        InvalidMembersCount = invalidCount;

        TryFinishValidation();
    }

    /// <summary>
    /// Attempts to finish validation with the current counts
    /// </summary>
    /// <returns>True if validation finished successfully, false otherwise</returns>
    private void TryFinishValidation()
    {
        if (ValidMembersCount + InvalidMembersCount == MembersCount)
        {
            SetStatus(InvalidMembersCount > 0 ? PolicyMemberUploadStatus.VALIDATING_ERROR : PolicyMemberUploadStatus.VALIDATED);
        }
    }

    /// <summary>
    /// Adds a validation error to the upload
    /// </summary>
    /// <param name="rowIndex">Row index where the error occurred</param>
    /// <param name="code">Error code</param>
    /// <param name="message">Error message</param>
    public void AddValidationError(int rowIndex, string code, string message)
    {
        var error = PolicyMemberUploadValidationError.Create(Id, rowIndex, code, message);
        ValidationErrors.Add(error);
    }

    /// <summary>
    /// Marks the upload as in progress and prepares it for validation by clearing old data
    /// </summary>
    public void MarkAsInProgress()
    {
        EnsureCanValidate();
        Status = PolicyMemberUploadStatus.VALIDATING;
        ValidMembersCount = 0;
        InvalidMembersCount = 0;
    }

    /// <summary>
    /// Marks the upload as completed based on current validation counts
    /// </summary>
    public void MarkAsCompleted() => TryFinishValidation();

    /// <summary>
    /// Processes validation results by setting counts and storing errors
    /// </summary>
    /// <param name="results">The validation orchestration results</param>
    public void ProcessValidationResults(ValidationOrchestrationResult results)
    {
        ValidationErrors.Clear();

        SetValidationCounts(results.ValidMembersCount, results.InvalidMembersCount);
        AddValidationErrors(results.MemberErrors);
    }

    /// <summary>
    /// Completes validation using event-driven approach by raising a domain event
    /// instead of directly managing validation errors
    /// </summary>
    /// <param name="results">The validation orchestration results</param>
    public void CompleteValidation(ValidationOrchestrationResult results)
    {
        SetValidationCounts(results.ValidMembersCount, results.InvalidMembersCount);
        TryFinishValidation();

        AddDomainEvent(new ValidationCompletedEvent(Id,
            results.ValidMembersCount, results.InvalidMembersCount, results.MemberErrors));
    }

    /// <summary>
    /// Adds multiple validation errors in bulk with proper formatting
    /// </summary>
    /// <param name="errorsByMember">Dictionary of errors indexed by member index</param>
    private void AddValidationErrors(Dictionary<int, List<ValidationError>> errorsByMember)
    {
        if (errorsByMember.Count == 0)
            return;

        foreach ((int memberIndex, List<ValidationError> errors) in errorsByMember)
        {
            int rowNumber = memberIndex + 1; // Convert to user-friendly row number

            foreach (ValidationError error in errors)
            {
                string userFriendlyMessage = FormatErrorMessage(error);
                AddValidationError(rowNumber, error.Code, userFriendlyMessage);
            }
        }
    }

    /// <summary>
    /// Checks if the upload can complete validation (is in a completable state)
    /// </summary>
    /// <returns>True if validation can be completed, false otherwise</returns>
    public bool CanCompleteValidation() =>
        Status == PolicyMemberUploadStatus.VALIDATED ||
        Status == PolicyMemberUploadStatus.VALIDATING_ERROR;

    /// <summary>
    /// Formats validation errors into user-friendly messages with helpful guidance
    /// </summary>
    /// <param name="error">The validation error to format</param>
    /// <returns>A user-friendly error message</returns>
    private static string FormatErrorMessage(ValidationError error) =>
        error.Code switch
        {
            ErrorCodes.InvalidFormat when error.PropertyPath.Contains("number") =>
                $"{error.Message}. For Excel files, please use the system number format. For CSV files, please enter numbers without commas (example: 12345.67).",
            ErrorCodes.InvalidOption when error.Context.ContainsKey("Options") =>
                $"{error.Message}, please use only {GetAvailableOptionsText(error.Context)}",
            _ => error.Message
        };

    /// <summary>
    /// Formats the available options for invalid option errors
    /// </summary>
    /// <param name="context">The error context containing options</param>
    /// <returns>Formatted options text</returns>
    private static string GetAvailableOptionsText(IReadOnlyDictionary<string, object?> context) =>
        context.TryGetValue("Options", out object? optionsValue)
            ? optionsValue switch
            {
                IEnumerable<string> stringOptions => string.Join(" or ", stringOptions),
                IEnumerable<object> objectOptions => string.Join(" or ", objectOptions.Select(o => o.ToString() ?? "")),
                _ => optionsValue?.ToString() ?? ""
            }
            : "";

    #endregion

    #region Status Management

    /// <summary>
    /// Marks the entire upload as failed
    /// </summary>
    public void FailUpload() => Status = PolicyMemberUploadStatus.FAILED;

    /// <summary>
    /// Starts the cancellation process with intelligent status transition based on current state
    /// </summary>
    /// <exception cref="InvalidPolicyMemberUploadStatusException">Thrown when the upload cannot be canceled</exception>
    public void StartCanceling()
    {
        // Ensure the upload can be canceled first
        EnsureCanCancel();

        if (Status == PolicyMemberUploadStatus.VALIDATING || Status == PolicyMemberUploadStatus.IMPORTING)
        {
            // For active processes, set status to CANCELING and wait for them to stop gracefully
            SetStatus(PolicyMemberUploadStatus.CANCELING);
        }
        else if (Status == PolicyMemberUploadStatus.REGISTERED ||
                 Status == PolicyMemberUploadStatus.VALIDATING_ERROR ||
                 Status == PolicyMemberUploadStatus.VALIDATED)
        {
            // For idle states, cancel immediately
            SetStatus(PolicyMemberUploadStatus.CANCELED);
        }
    }

    /// <summary>
    /// Completes the cancellation process
    /// </summary>
    /// <exception cref="InvalidPolicyMemberUploadStatusException">Thrown when the upload is not in canceling status</exception>
    public void CompleteCancellation()
    {
        if (Status != PolicyMemberUploadStatus.CANCELING)
            throw new InvalidPolicyMemberUploadStatusException(Id.Value.ToString(), Status.Value, [PolicyMemberUploadStatus.CANCELING.Value]);

        Status = PolicyMemberUploadStatus.CANCELED;
    }

    /// <summary>
    /// Domain method: Checks if upload has imported members that need cleanup
    /// This is business logic that belongs in the domain entity
    /// </summary>
    private bool HasImportedMembers() =>
        ImportedResults.Any(r => r is { Success: true, PolicyMemberId: not null });

    /// <summary>
    /// Domain method: Gets count of successfully imported members
    /// </summary>
    public int GetImportedMembersCount() =>
        ImportedResults.Count(r => r is { Success: true, PolicyMemberId: not null });

    /// <summary>
    /// Domain method: Gets all imported member IDs for cleanup operations
    /// </summary>
    public List<PolicyMemberId> GetImportedMemberIds() =>
        [.. ImportedResults
            .Where(r => r is { Success: true, PolicyMemberId: not null })
            .Select(r => r.PolicyMemberId!)];

    /// <summary>
    /// Domain method: Determines if member cleanup is required during cancellation
    /// Business rule: Only cleanup if we're canceling and have imported members
    /// </summary>
    public bool RequiresMemberCleanup() =>
        Status == PolicyMemberUploadStatus.CANCELING && HasImportedMembers();

    #endregion

    #region Private Methods

    /// <summary>
    /// Ensures the upload status is one of the expected statuses
    /// </summary>
    /// <param name="expectedStatuses">Array of acceptable statuses</param>
    /// <exception cref="InvalidPolicyMemberUploadStatusException">Thrown when status is not in expected statuses</exception>
    private void EnsureStatusIs(params PolicyMemberUploadStatus[] expectedStatuses)
    {
        if (expectedStatuses.Contains(Status)) return;
        string[] expectedStatusValues = [.. expectedStatuses.Select(s => s.Value)];
        throw new InvalidPolicyMemberUploadStatusException(Id.Value.ToString(), Status.Value, expectedStatusValues);
    }

    /// <summary>
    /// Ensures the upload can be canceled
    /// </summary>
    /// <exception cref="InvalidPolicyMemberUploadStatusException">Thrown when the upload cannot be canceled</exception>
    public void EnsureCanCancel()
    {
        PolicyMemberUploadStatus[] allowedStatuses =
        [
            PolicyMemberUploadStatus.REGISTERED,
            PolicyMemberUploadStatus.VALIDATING,
            PolicyMemberUploadStatus.VALIDATING_ERROR,
            PolicyMemberUploadStatus.VALIDATED,
            PolicyMemberUploadStatus.IMPORTING
        ];

        if (allowedStatuses.Contains(Status)) return;
        string[] allowedStatusValues = [.. allowedStatuses.Select(s => s.Value)];
        throw new InvalidPolicyMemberUploadStatusException(Id.Value.ToString(), Status.Value, allowedStatusValues);
    }

    #endregion
}