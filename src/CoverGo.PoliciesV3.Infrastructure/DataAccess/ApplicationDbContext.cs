using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Enums;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Endorsements;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.Policies;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMembers;
using CoverGo.PoliciesV3.Infrastructure.DataAccess.Configurations.PolicyMemberUploads;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Infrastructure.DataAccess;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, IMediator? mediator = null) : DbContext(options)
{
    private readonly IMediator? _mediator = mediator;
    #region DbSets - Policies

    public DbSet<Policy> Policies => Set<Policy>();

    #endregion

    #region DbSets - Endorsements

    public DbSet<Endorsement> Endorsements => Set<Endorsement>();

    #endregion

    #region DbSets - Policy Members

    public DbSet<PolicyMember> PolicyMembers => Set<PolicyMember>();
    public DbSet<PolicyMemberState> PolicyMemberStates => Set<PolicyMemberState>();

    #endregion

    #region DbSets - Policy Member Uploads

    public DbSet<PolicyMemberUpload> PolicyMemberUploads => Set<PolicyMemberUpload>();
    public DbSet<PolicyMemberUploadValidationError> PolicyMemberUploadValidationErrors => Set<PolicyMemberUploadValidationError>();
    public DbSet<PolicyMemberUploadImportedResult> PolicyMemberUploadImportedResults => Set<PolicyMemberUploadImportedResult>();

    #endregion

    #region Domain Event Dispatching

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Dispatch domain events before saving changes
        await DispatchDomainEventsAsync(cancellationToken);

        // Save changes to database
        return await base.SaveChangesAsync(cancellationToken);
    }

    private async Task DispatchDomainEventsAsync(CancellationToken cancellationToken)
    {
        if (_mediator == null) return;

        // Get all entities with domain events
        var entitiesWithEvents = ChangeTracker.Entries()
            .Where(entry => entry.Entity is IAggregateRoot)
            .Select(entry => (IAggregateRoot)entry.Entity)
            .Where(entity => entity.DomainEvents.Count > 0)
            .ToList();

        // Get all domain events
        var domainEvents = entitiesWithEvents
            .SelectMany(entity => entity.DomainEvents.Cast<IDomainEvent>())
            .ToList();

        // Clear domain events from entities
        entitiesWithEvents.ForEach(entity => entity.ClearDomainEvents());

        // Dispatch all domain events
        foreach (IDomainEvent domainEvent in domainEvents)
        {
            await _mediator.Publish(domainEvent, cancellationToken);
        }
    }

    #endregion

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all entity configurations from assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

        // Configure PostgreSQL xmin concurrency control for all entities with RowVersion property
        // This is applied after entity configurations to ensure it consistently overrides any conflicting mappings
        //
        // Type Constraint: Currently limited to 'uint' properties to match PostgreSQL xmin system column behavior.
        // PostgreSQL xmin is a 32-bit transaction ID, making uint the most appropriate .NET type.
        // Future Enhancement: Could be extended to support other numeric types (ulong, int) if needed,
        // but uint provides the best semantic match for PostgreSQL xmin values.
        modelBuilder.Model.GetEntityTypes()
            .Where(entityType =>
            {
                System.Reflection.PropertyInfo? rowVersionProperty = entityType.ClrType.GetProperty(DatabaseConstants.PostgreSql.SystemColumnPropertyNames.RowVersion);
                return rowVersionProperty != null && rowVersionProperty.PropertyType == typeof(uint);
            })
            .ToList()
            .ForEach(entityType => modelBuilder.Entity(entityType.ClrType).Property<uint>(DatabaseConstants.PostgreSql.SystemColumnPropertyNames.RowVersion)
                    .HasColumnName(PostgreSqlHelpers.GetColumnName(DatabaseConstants.PostgreSql.SystemColumnPropertyNames.RowVersion))
                    .HasColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Xid)
                    .ValueGeneratedOnAddOrUpdate()
                    .IsConcurrencyToken());
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        base.ConfigureConventions(configurationBuilder);

        #region Policy Converters

        configurationBuilder.Properties<PolicyId>()
            .HaveConversion<PolicyConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyStatus>()
            .HaveConversion<PolicyConverter.StatusConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);

        #endregion

        #region Policy Member Converters

        configurationBuilder.Properties<PolicyMemberId>()
            .HaveConversion<PolicyMemberConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberId?>()
            .HaveConversion<PolicyMemberConverter.NullableIdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberStateId>()
            .HaveConversion<PolicyMemberStateConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberValidationResult>()
            .HaveConversion<PolicyMemberConverter.ValidationResultConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);

        configurationBuilder.Properties<PolicyMemberUnderwritingResult>()
            .HaveConversion<PolicyMemberConverter.UnderwritingResultConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);

        #endregion

        #region Endorsement Converters

        configurationBuilder.Properties<EndorsementId>()
            .HaveConversion<EndorsementConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<EndorsementId?>()
            .HaveConversion<EndorsementConverter.NullableIdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        #endregion

        #region Policy Member Upload Converters

        configurationBuilder.Properties<PolicyMemberUploadId>()
            .HaveConversion<PolicyMemberUploadConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        configurationBuilder.Properties<PolicyMemberUploadStatus>()
            .HaveConversion<PolicyMemberUploadConverter.StatusConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Varchar255);

        configurationBuilder.Properties<PolicyMemberUploadValidationErrorId>()
            .HaveConversion<PolicyMemberUploadValidationErrorConverter.IdConverter>()
            .HaveColumnType(DatabaseConstants.PostgreSql.ColumnTypes.Uuid);

        #endregion
    }
}