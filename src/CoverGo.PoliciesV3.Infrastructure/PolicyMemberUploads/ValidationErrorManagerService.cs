using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;

/// <summary>
/// Service for managing validation errors in an event-driven approach
/// </summary>
public class ValidationErrorManagerService(
    IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId> validationErrorRepository,
    ILogger<ValidationErrorManagerService> logger) : IValidationErrorManagerService
{
    public async Task ClearPreviousValidationErrorsAsync(PolicyMemberUploadId uploadId, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogDebug("Clearing previous validation errors for upload {UploadId}", uploadId);

            // Find all existing validation errors for this upload
            var existingErrors = await validationErrorRepository.FindAllByAsync(
                error => error.PolicyMemberUploadId == uploadId,
                cancellationToken);

            if (existingErrors.Any())
            {
                // Delete existing errors
                await validationErrorRepository.DeleteBatchAsync(existingErrors.ToList(), cancellationToken);
                logger.LogDebug("Cleared {ErrorCount} previous validation errors for upload {UploadId}",
                    existingErrors.Count(), uploadId);
            }
            else
            {
                logger.LogDebug("No previous validation errors found for upload {UploadId}", uploadId);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to clear previous validation errors for upload {UploadId}", uploadId);
            throw;
        }
    }

    public async Task PersistValidationErrorsAsync(
        PolicyMemberUploadId uploadId,
        Dictionary<int, List<ValidationError>> memberErrors,
        CancellationToken cancellationToken)
    {
        if (memberErrors.Count == 0)
        {
            logger.LogDebug("No validation errors to persist for upload {UploadId}", uploadId);
            return;
        }

        try
        {
            logger.LogDebug("Persisting validation errors for upload {UploadId}", uploadId);

            // Convert validation errors to domain entities
            var validationErrorEntities = new List<PolicyMemberUploadValidationError>();

            foreach ((int memberIndex, List<ValidationError> errors) in memberErrors)
            {
                int rowNumber = memberIndex + 1; // Convert to user-friendly row number

                foreach (ValidationError error in errors)
                {
                    string userFriendlyMessage = FormatErrorMessage(error);
                    var errorEntity = PolicyMemberUploadValidationError.Create(uploadId, rowNumber, error.Code, userFriendlyMessage);
                    validationErrorEntities.Add(errorEntity);
                }
            }

            // Persist all errors in batch
            await validationErrorRepository.InsertBatchAsync(validationErrorEntities, cancellationToken);

            logger.LogInformation("Successfully persisted {ErrorCount} validation errors for upload {UploadId}",
                validationErrorEntities.Count, uploadId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to persist validation errors for upload {UploadId}", uploadId);
            throw;
        }
    }

    /// <summary>
    /// Formats validation errors into user-friendly messages with helpful guidance
    /// This mirrors the logic from PolicyMemberUpload.FormatErrorMessage
    /// </summary>
    /// <param name="error">The validation error to format</param>
    /// <returns>A user-friendly error message</returns>
    private static string FormatErrorMessage(ValidationError error) =>
        error.Code switch
        {
            ErrorCodes.InvalidFormat when error.PropertyPath.Contains("number") =>
                $"{error.Message}. For Excel files, please use the system number format. For CSV files, please enter numbers without commas (example: 12345.67).",
            ErrorCodes.InvalidOption when error.Context.ContainsKey("Options") =>
                $"{error.Message}, please use only {GetAvailableOptionsText(error.Context)}",
            _ => error.Message
        };

    /// <summary>
    /// Formats the available options for invalid option errors
    /// This mirrors the logic from PolicyMemberUpload.GetAvailableOptionsText
    /// </summary>
    /// <param name="context">The error context containing options</param>
    /// <returns>Formatted options text</returns>
    private static string GetAvailableOptionsText(IReadOnlyDictionary<string, object?> context) =>
        context.TryGetValue("Options", out object? optionsValue)
            ? optionsValue switch
            {
                IEnumerable<string> stringOptions => string.Join(" or ", stringOptions),
                IEnumerable<object> objectOptions => string.Join(" or ", objectOptions.Select(o => o.ToString() ?? "")),
                _ => optionsValue?.ToString() ?? ""
            }
            : "";
}
