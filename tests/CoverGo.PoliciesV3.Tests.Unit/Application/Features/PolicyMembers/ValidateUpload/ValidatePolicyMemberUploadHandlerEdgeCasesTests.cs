using System.Diagnostics;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.Common;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for edge cases and boundary conditions in ValidatePolicyMemberUploadHandler.
/// Covers data volume limits, boundary values, system resources, data quality, and business logic edge cases.
/// </summary>
public class ValidatePolicyMemberUploadHandlerEdgeCasesTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerEdgeCasesTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());
        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Setup mock for IValidationErrorAggregator to return a valid result
        var mockValidationErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockValidationErrorAggregator.Setup(x => x.AggregateResults(
                It.IsAny<List<BatchValidationResult>>(),
                It.IsAny<int>()))
            .Returns(new BatchValidationResult
            {
                ValidCount = 0,
                InvalidCount = 0,
                RowErrors = []
            });

        // Create real CompleteUploadValidationSpecification for integration testing
        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            mockValidationErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Create handler with real specifications
        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Data Volume Edge Cases

    [Fact]
    public async Task Handle_WithEmptyDataset_ShouldReturnSuccess()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create empty dataset (0 members)
        List<IReadOnlyDictionary<string, string?>> emptyData = [];

        SetupValidationScenario(emptyData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("empty datasets are valid - there's nothing to validate but the upload structure is correct");
        result.Value.Should().NotBeNull();
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify no member-specific validations were called for empty dataset
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_WithSingleMemberUpload_ShouldProcessEfficiently()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create single member dataset
        List<IReadOnlyDictionary<string, string?>> singleMemberData = CreateSingleMemberData();

        SetupValidationScenario(singleMemberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Stopwatch stopwatch = Stopwatch.StartNew();
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue("single member uploads should process successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify performance is optimal for single member
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(100, "single member processing should be very fast");
    }

    [Fact]
    public async Task Handle_WithMaximumDatasetLimit_ShouldHandleWithinMemoryConstraints()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create maximum dataset (15,000 members to test upper limits)
        List<IReadOnlyDictionary<string, string?>> maxDataset = CreateMaximumDataset(15000);

        SetupValidationScenario(maxDataset, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        long memoryBefore = GC.GetTotalMemory(true);
        Stopwatch stopwatch = Stopwatch.StartNew();
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        stopwatch.Stop();
        long memoryAfter = GC.GetTotalMemory(true);
        long memoryUsed = memoryAfter - memoryBefore;

        // Assert
        result.IsSuccess.Should().BeTrue("maximum dataset should be handled successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify performance and memory constraints
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(10000, "maximum dataset processing should complete within 10 seconds");
        memoryUsed.Should().BeLessThan(100 * 1024 * 1024, "memory usage should stay under 100MB for 15k members");
    }

    [Fact]
    public async Task Handle_WithAllDuplicateData_ShouldHandleHighErrorRate()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create dataset where every member has validation errors (100% error rate)
        List<IReadOnlyDictionary<string, string?>> allDuplicateData = CreateAllDuplicateData(2000);

        SetupValidationScenario(allDuplicateData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup uniqueness services to return conflicts for all members
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["email", "hkid"]);

        // Act
        Stopwatch stopwatch = Stopwatch.StartNew();
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with 100% error rate");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify performance with high error aggregation
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "high error rate processing should complete within 5 seconds");
    }

    [Fact]
    public async Task Handle_WithMixedDataQuality_ShouldProcessVariedScenarios()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create mixed quality dataset (valid, invalid, edge cases)
        List<IReadOnlyDictionary<string, string?>> mixedData = CreateMixedQualityData();

        SetupValidationScenario(mixedData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup mixed validation results
        SetupMixedValidationResults();

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("mixed data quality should be handled correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify that different data quality scenarios were processed appropriately
    }

    #endregion

    #region Boundary Value Testing

    [Fact]
    public async Task Handle_WithMinimumFieldLengths_ShouldValidateCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with minimum field lengths
        List<IReadOnlyDictionary<string, string?>> minLengthData = CreateMinimumFieldLengthData();

        SetupValidationScenario(minLengthData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("minimum field lengths should be accepted");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithMaximumFieldLengths_ShouldValidateCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with maximum field lengths
        List<IReadOnlyDictionary<string, string?>> maxLengthData = CreateMaximumFieldLengthData();

        SetupValidationScenario(maxLengthData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("maximum field lengths should be accepted");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithDateBoundaryConditions_ShouldHandleEdgeCases()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with date boundary conditions
        List<IReadOnlyDictionary<string, string?>> dateBoundaryData = CreateDateBoundaryData();

        SetupValidationScenario(dateBoundaryData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("date boundary conditions should be handled correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithLeapYearDates_ShouldValidateCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with leap year dates (February 29th)
        List<IReadOnlyDictionary<string, string?>> leapYearData = CreateLeapYearData();

        SetupValidationScenario(leapYearData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("leap year dates should be validated correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithNumericBoundaryValues_ShouldHandleEdgeCases()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with numeric boundary values
        List<IReadOnlyDictionary<string, string?>> numericBoundaryData = CreateNumericBoundaryData();

        SetupValidationScenario(numericBoundaryData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("numeric boundary values should be handled correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithSpecialCharacters_ShouldProcessCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with special characters
        List<IReadOnlyDictionary<string, string?>> specialCharData = CreateSpecialCharacterData();

        SetupValidationScenario(specialCharData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("special characters should be processed correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithUnicodeAndInternationalization_ShouldHandleCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with Unicode and international characters
        List<IReadOnlyDictionary<string, string?>> unicodeData = CreateUnicodeData();

        SetupValidationScenario(unicodeData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("Unicode and international characters should be handled correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region System Resource Edge Cases

    [Fact]
    public async Task Handle_WithMemoryPressureScenario_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create large dataset to simulate memory pressure
        List<IReadOnlyDictionary<string, string?>> largeDataset = CreateLargeDatasetForMemoryTest(10000);

        SetupValidationScenario(largeDataset, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Force garbage collection before test
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        // Act
        long memoryBefore = GC.GetTotalMemory(false);
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        long memoryAfter = GC.GetTotalMemory(false);
        long memoryUsed = memoryAfter - memoryBefore;

        // Assert
        result.IsSuccess.Should().BeTrue("memory pressure scenarios should be handled gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify memory usage is reasonable
        double memoryPerMember = (double)memoryUsed / largeDataset.Count;
        memoryPerMember.Should().BeLessThan(5000, "memory per member should be under 5KB");
    }

    [Fact]
    public async Task Handle_WithCancellationToken_ShouldRespectCancellation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        List<IReadOnlyDictionary<string, string?>> testData = CreateTimeConsumingDataset(100); // Smaller dataset for faster test

        // Setup basic scenario but override file processing to check cancellation immediately
        SetupValidationScenario(testData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Override file processing to check cancellation token immediately
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, string, CancellationToken>((_, _, ct) =>
            {
                // Check cancellation immediately without any delay
                ct.ThrowIfCancellationRequested();
                return Task.FromResult(FileProcessingResult.Success(testData, testData.Count));
            });

        // Use pre-cancelled token for deterministic behavior
        using CancellationTokenSource cts = new CancellationTokenSource();
        cts.Cancel(); // Cancel immediately

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _handler.Handle(command, cts.Token));
    }

    [Fact]
    public async Task Handle_WithCancelledTokenDuringPolicyRetrieval_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Setup policy service to check cancellation token immediately
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns<string, CancellationToken>((_, ct) =>
            {
                // Check cancellation immediately without delay
                ct.ThrowIfCancellationRequested();
                return Task.FromResult<PolicyDto?>(CreateValidPolicy());
            });

        // Use pre-cancelled token for deterministic behavior
        using CancellationTokenSource cts = new CancellationTokenSource();
        cts.Cancel(); // Cancel immediately

        // Act & Assert
        await Assert.ThrowsAsync<OperationCanceledException>(() =>
            _handler.Handle(command, cts.Token));
    }

    #endregion

    #region Data Quality Edge Cases

    [Fact]
    public async Task Handle_WithMalformedDataFormats_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with malformed formats
        List<IReadOnlyDictionary<string, string?>> malformedData = CreateMalformedData();

        SetupValidationScenario(malformedData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("malformed data should be handled gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithInconsistentDataTypes_ShouldValidateCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with inconsistent data types
        List<IReadOnlyDictionary<string, string?>> inconsistentData = CreateInconsistentDataTypes();

        SetupValidationScenario(inconsistentData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("inconsistent data types should be validated correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithMissingRequiredFields_ShouldDetectValidationErrors()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with missing required fields
        List<IReadOnlyDictionary<string, string?>> missingFieldsData = CreateMissingRequiredFieldsData();

        SetupValidationScenario(missingFieldsData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithCrossFieldValidationDependencies_ShouldValidateCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create data with cross-field validation dependencies
        List<IReadOnlyDictionary<string, string?>> crossFieldData = CreateCrossFieldValidationData();

        SetupValidationScenario(crossFieldData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("cross-field validation dependencies should be handled correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Business Logic Edge Cases

    [Fact]
    public async Task Handle_WithPolicyStateTransitionDuringValidation_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup policy service to return different states on subsequent calls
        int callCount = 0;
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(() =>
            {
                callCount++;
                PolicyDto policyDto = callCount == 1
                    ? CreateValidPolicy()
                    : CreateValidPolicyWithIssuedState(); // Policy state changed during validation
                return Task.FromResult<PolicyDto?>(policyDto);
            });

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("policy state transitions should be handled gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithEndorsementLifecycleEdgeCases_ShouldHandleCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUploadWithEndorsement();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("endorsement lifecycle edge cases should be handled correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithContractHolderChangesDuringValidation_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup contract holder policies to change during validation
        int callCount = 0;
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(() =>
            {
                callCount++;
                return Task.FromResult<List<string>>(callCount == 1
                    ? ["POLICY-001", "POLICY-002"]
                    : ["POLICY-001", "POLICY-002", "POLICY-003"]); // Contract holder changed
            });

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("contract holder changes should be handled gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithProductConfigurationChangesDuringProcessing_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup product service to return different configurations on subsequent calls
        int callCount = 0;
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .Returns(() =>
            {
                callCount++;
                List<string> plans = callCount == 1
                    ? ["PLAN-001", "PLAN-002"]
                    : ["PLAN-001", "PLAN-002", "PLAN-003"]; // Product config changed
                return Task.FromResult<IReadOnlyList<string>?>(plans);
            });

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("product configuration changes should be handled gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();

    private PolicyMemberUpload CreateValidUploadWithEndorsement()
    {
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();
        // Create a new upload with endorsement ID using the Create method
        return PolicyMemberUpload.Create(
            upload.PolicyId,
            upload.Path,
            upload.MembersCount,
            Guid.NewGuid());
    }

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyDto CreateValidPolicyWithIssuedState()
    {
        PolicyDto policy = MemberUploadTestDataBuilder.Create().BuildPolicyDto();
        return new PolicyDto
        {
            Id = policy.Id,
            ProductId = policy.ProductId,
            ContractHolderId = policy.ContractHolderId,
            StartDate = policy.StartDate,
            EndDate = policy.EndDate,
            IsIssued = true, // Changed state
            Endorsements = policy.Endorsements,
            ApprovedEndorsementIds = policy.ApprovedEndorsementIds
        };
    }

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateValidMemberData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John" },
            { "lastName", "Doe" },
            { "name", "John Doe" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF001" }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateSingleMemberData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "Single" },
            { "lastName", "Member" },
            { "name", "Single Member" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "S123456(1)" },
            { "staffNo", "SINGLE001" }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMaximumDataset(int count)
    {
        List<IReadOnlyDictionary<string, string?>> dataset = [];

        for (int i = 1; i <= count; i++)
        {
            Dictionary<string, string?> member = new Dictionary<string, string?>
            {
                { "planId", $"PLAN-{(i % 3) + 1:D3}" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", $"FirstName{i}" },
                { "lastName", $"LastName{i}" },
                { "name", $"FirstName{i} LastName{i}" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30 - (i % 20)).ToString("yyyy-MM-dd") },
                { "email", $"member{i}@test.com" },
                { "hkid", $"A{i:D6}({(i % 10)})" },
                { "staffNo", $"STAFF{i:D6}" }
            };

            dataset.Add(member);
        }

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateAllDuplicateData(int count)
    {
        List<IReadOnlyDictionary<string, string?>> dataset = [];

        // Create members with intentionally duplicate data to trigger validation errors
        for (int i = 1; i <= count; i++)
        {
            Dictionary<string, string?> member = new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Duplicate" },
                { "lastName", "Member" },
                { "name", "Duplicate Member" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" }, // Same email for all
                { "hkid", "A123456(7)" }, // Same HKID for all
                { "staffNo", $"STAFF{i:D3}" } // Different staff numbers
            };

            dataset.Add(member);
        }

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMixedQualityData()
    {
        List<IReadOnlyDictionary<string, string?>> dataset =
        [
            // Valid member
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Valid" },
                { "lastName", "Member" },
                { "name", "Valid Member" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "A123456(7)" },
                { "staffNo", "VALID001" }
            },
            // Member with missing required field
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-002" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Missing" },
                { "lastName", "Email" },
                { "name", "Missing Email" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
                { "email", null }, // Missing email
                { "hkid", "B123456(8)" },
                { "staffNo", "MISSING001" }
            },
            // Member with invalid date format
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-003" },
                { "effectiveDate", "invalid-date" }, // Invalid date format
                { "firstName", "Invalid" },
                { "lastName", "Date" },
                { "name", "Invalid Date" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-35).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "C123456(9)" },
                { "staffNo", "INVALID001" }
            },
        ];

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMinimumFieldLengthData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "P1" }, // Minimum length
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "A" }, // Single character
            { "lastName", "B" }, // Single character
            { "name", "A B" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-18).ToString("yyyy-MM-dd") }, // Minimum age
            { "email", "<EMAIL>" }, // Minimum valid email
            { "hkid", "A123456(7)" },
            { "staffNo", "1" } // Single character
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMaximumFieldLengthData()
    {
        string longString = new('A', 255); // Maximum typical field length
        string longEmail = $"{new string('a', 240)}@test.com"; // Long but valid email

        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", longString.Substring(0, 50) }, // Reasonable plan ID length
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", longString.Substring(0, 100) },
            { "lastName", longString.Substring(0, 100) },
            { "name", $"{longString.Substring(0, 100)} {longString.Substring(0, 100)}" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-65).ToString("yyyy-MM-dd") }, // Maximum reasonable age
            { "email", longEmail },
            { "hkid", "A123456(7)" },
            { "staffNo", longString.Substring(0, 50) }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateDateBoundaryData()
    {
        List<IReadOnlyDictionary<string, string?>> dataset =
        [
            // Past boundary date
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" },
                { "effectiveDate", new DateTime(1900, 1, 1).ToString("yyyy-MM-dd") }, // Very old date
                { "firstName", "Past" },
                { "lastName", "Boundary" },
                { "name", "Past Boundary" },
                { "memberType", "employee" },
                { "dateOfBirth", new DateTime(1900, 1, 1).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "P123456(1)" },
                { "staffNo", "PAST001" }
            },
            // Future boundary date
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-002" },
                { "effectiveDate", new DateTime(2099, 12, 31).ToString("yyyy-MM-dd") }, // Far future date
                { "firstName", "Future" },
                { "lastName", "Boundary" },
                { "name", "Future Boundary" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "F123456(2)" },
                { "staffNo", "FUTURE001" }
            },
        ];

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateLeapYearData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", "2024-02-29" }, // Leap year date
            { "firstName", "Leap" },
            { "lastName", "Year" },
            { "name", "Leap Year" },
            { "memberType", "employee" },
            { "dateOfBirth", "2000-02-29" }, // Born on leap year
            { "email", "<EMAIL>" },
            { "hkid", "L123456(3)" },
            { "staffNo", "LEAP001" }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateNumericBoundaryData()
    {
        List<IReadOnlyDictionary<string, string?>> dataset =
        [
            // Zero values
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Zero" },
                { "lastName", "Values" },
                { "name", "Zero Values" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "Z123456(0)" },
                { "staffNo", "0" },
                { "salary", "0" }
            },
            // Maximum values
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-002" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Maximum" },
                { "lastName", "Values" },
                { "name", "Maximum Values" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "M123456(9)" },
                { "staffNo", "999999" },
                { "salary", "999999999" }
            },
        ];

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateSpecialCharacterData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John-Paul" }, // Hyphen
            { "lastName", "O'Connor" }, // Apostrophe
            { "name", "John-Paul O'Connor" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" }, // Special chars in email
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF-001" }, // Hyphen in staff number
            { "address", "123 Main St., Apt. #4B" } // Various special characters
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateUnicodeData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "張" }, // Chinese character
            { "lastName", "三" }, // Chinese character
            { "name", "張三" }, // Chinese name
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "UNICODE001" },
            { "address", "東京都渋谷区" }, // Japanese address
            { "notes", "Émployé spécialisé" } // French with accents
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateLargeDatasetForMemoryTest(int count)
    {
        List<IReadOnlyDictionary<string, string?>> dataset = [];

        for (int i = 1; i <= count; i++)
        {
            Dictionary<string, string?> member = new Dictionary<string, string?>
            {
                { "planId", $"PLAN-{i % 5 + 1:D3}" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", $"MemoryTest{i}" },
                { "lastName", $"Member{i}" },
                { "name", $"MemoryTest{i} Member{i}" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30 - (i % 30)).ToString("yyyy-MM-dd") },
                { "email", $"memory.test{i}@test.com" },
                { "hkid", $"M{i:D6}({(i % 10)})" },
                { "staffNo", $"MEM{i:D6}" },
                { "address", $"Memory Test Address {i}, Floor {i % 50}, Unit {i % 100}" },
                { "phone", $"+852-{i:D4}-{i:D4}" },
                { "emergencyContact", $"Emergency Contact {i}" },
                { "notes", $"Memory test member {i} with extended data for memory pressure testing" }
            };

            dataset.Add(member);
        }

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateTimeConsumingDataset(int count)
    {
        List<IReadOnlyDictionary<string, string?>> dataset = [];

        for (int i = 1; i <= count; i++)
        {
            Dictionary<string, string?> member = new Dictionary<string, string?>
            {
                { "planId", $"PLAN-{i % 3 + 1:D3}" },
                { "effectiveDate", TestDateConstants.PolicyDates.EffectiveDate.ToString("yyyy-MM-dd") },
                { "firstName", $"TimeTest{i}" },
                { "lastName", $"Member{i}" },
                { "name", $"TimeTest{i} Member{i}" },
                { "memberType", "employee" },
                { "dateOfBirth", TestDateConstants.ReferenceDate.AddYears(-30 - (i % 20)).ToString("yyyy-MM-dd") },
                { "email", $"time.test{i}@test.com" },
                { "hkid", $"T{i:D6}({(i % 10)})" },
                { "staffNo", $"TIME{i:D6}" }
            };

            dataset.Add(member);
        }

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMalformedData()
    {
        List<IReadOnlyDictionary<string, string?>> dataset =
        [
            // Malformed date
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" },
                { "effectiveDate", "2024-13-45" }, // Invalid date
                { "firstName", "Malformed" },
                { "lastName", "Date" },
                { "name", "Malformed Date" },
                { "memberType", "employee" },
                { "dateOfBirth", "invalid-date-format" },
                { "email", "<EMAIL>" },
                { "hkid", "M123456(7)" },
                { "staffNo", "MAL001" }
            },
            // Malformed email
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-002" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Malformed" },
                { "lastName", "Email" },
                { "name", "Malformed Email" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "email", "not-an-email" }, // Invalid email format
                { "hkid", "M123457(8)" },
                { "staffNo", "MAL002" }
            },
        ];

        return dataset;
    }

    private List<IReadOnlyDictionary<string, string?>> CreateInconsistentDataTypes()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "Inconsistent" },
            { "lastName", "Types" },
            { "name", "Inconsistent Types" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "I123456(7)" },
            { "staffNo", "INC001" },
            { "salary", "not-a-number" }, // String where number expected
            { "isActive", "maybe" } // String where boolean expected
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMissingRequiredFieldsData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", null }, // Missing required field
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", null }, // Missing required field
            { "lastName", "Missing" },
            { "name", "Missing Fields" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", null }, // Missing required field
            { "hkid", "M123456(7)" },
            { "staffNo", "MISS001" }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateCrossFieldValidationData()
    {
        List<IReadOnlyDictionary<string, string?>> dataset =
        [
            // Employee with dependent relationship (cross-field validation)
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" },
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Primary" },
                { "lastName", "Employee" },
                { "name", "Primary Employee" },
                { "memberType", "employee" },
                { "dateOfBirth", DateTime.Today.AddYears(-35).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "P123456(7)" },
                { "staffNo", "PRIMARY001" },
                { "memberId", "PRIMARY-001" }
            },
            // Dependent with reference to primary (cross-field validation)
            new Dictionary<string, string?>
            {
                { "planId", "PLAN-001" }, // Must match primary's plan
                { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
                { "firstName", "Dependent" },
                { "lastName", "Child" },
                { "name", "Dependent Child" },
                { "memberType", "dependent" },
                { "dateOfBirth", DateTime.Today.AddYears(-10).ToString("yyyy-MM-dd") },
                { "email", "<EMAIL>" },
                { "hkid", "D123456(8)" },
                { "primaryMemberId", "PRIMARY-001" }, // Cross-field reference
                { "relationship", "child" }
            },
        ];

        return dataset;
    }

    private void SetupValidationScenario(
        List<IReadOnlyDictionary<string, string?>> memberData,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        ResolvedValidationData? resolvedData = null)
    {
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto testPolicy = policy ?? CreateValidPolicy();
        PolicyMemberFieldsSchema testSchema = schema ?? CreateTestSchema();
        _ = resolvedData ?? ResolvedValidationDataTestDataBuilder.Create();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testPolicy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testSchema);

        // Setup file processing service to return test member data
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // If your validation context is constructed via a service or factory, mock it here to return the context with testResolvedData.
        // (If not, ensure downstream code can access testResolvedData as required.)

        SetupDefaultMocks();
    }

    private void SetupMixedValidationResults()
    {
        // Setup some validations to pass, others to fail
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["email"]); // Some conflicts

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]); // No conflicts
    }

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Setup default policy service responses
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default users service responses
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default member query service responses
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default uniqueness service responses (no conflicts)
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(), It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(), It.IsAny<PolicyMemberId?>(), It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(), It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup repository operations
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(), It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(), It.IsAny<int>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    #endregion
}