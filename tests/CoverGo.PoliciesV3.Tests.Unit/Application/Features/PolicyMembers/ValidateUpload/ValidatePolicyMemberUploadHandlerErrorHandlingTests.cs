using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for error handling and message formatting in ValidatePolicyMemberUploadHandler.
/// Covers exception translation, error message enhancement, and context-aware error formatting.
/// </summary>
public class ValidatePolicyMemberUploadHandlerErrorHandlingTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerErrorHandlingTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());

        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Create real CompleteUploadValidationSpecification for integration testing
        var mockErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns((List<BatchValidationResult> results, int _) =>
            {
                int totalValid = results.Sum(r => r.ValidCount);
                int totalInvalid = results.Sum(r => r.InvalidCount);
                var allErrors = results.SelectMany(r => r.RowErrors).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                return BatchValidationResult.WithErrors(totalValid, totalInvalid, allErrors);
            });

        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            mockErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        // Create handler with real specifications
        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Exception Translation Tests

    [Fact]
    public void CreateMemberIdValidationError_WithMemberNotFoundException_ShouldReturnMemberNotFoundError()
    {
        // Arrange
        string memberId = "NONEXISTENT-MEMBER";
        MemberNotFoundException exception = new MemberNotFoundException(memberId);

        // Act
        List<ValidationError> result = CreateMemberIdValidationError(exception, memberId);

        // Assert
        result.Should().ContainSingle(e => e.Code == "MEMBER_NOT_FOUND");
        result.First().PropertyPath.Should().Be("memberId");
        result.First().PropertyLabel.Should().Be("Member ID");
    }

    [Fact]
    public void CreateMemberIdValidationError_WithPolicyMemberExistsException_ShouldReturnMemberIdTakenError()
    {
        // Arrange
        string memberId = "EXISTING-MEMBER";
        Guid policyMemberId = Guid.NewGuid();
        PolicyMemberExistsException exception = new PolicyMemberExistsException(policyMemberId, memberId);

        // Act
        List<ValidationError> result = CreateMemberIdValidationError(exception, memberId);

        // Assert
        result.Should().ContainSingle(e => e.Code == "MEMBER_ID_TAKEN");
        result.First().PropertyPath.Should().Be("memberId");
        result.First().PropertyLabel.Should().Be("Member ID");
    }

    [Fact]
    public async Task Handle_WithSystemException_ShouldPropagateException()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Setup policy service to throw system exception
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Database connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _handler.Handle(command, CancellationToken.None));

        // Verify that the exception was not caught and converted to a validation error
    }

    [Fact]
    public async Task Handle_WithDomainException_ShouldConvertToValidationError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();

        // Create policy with IsIssued = true to trigger domain exception
        PolicyDto policy = new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Type = "health", Plan = "basic", Version = "v1" },
            ContractHolderId = Guid.NewGuid().ToString(),
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            IsIssued = true, // This will cause ValidateForMemberUpload to throw
            Endorsements = [],
            ApprovedEndorsementIds = []
        };

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue("domain exceptions should be converted to validation errors");
        result.Errors.Should().ContainSingle();
        result.Errors[0].PropertyPath.Should().Be("policy");
    }

    #endregion

    #region Error Message Enhancement Tests

    [Fact]
    public void FormatValidationErrorMessage_WithInvalidFormatNumberField_ShouldReturnEnhancedMessage()
    {
        // Arrange
        ValidationError error = new ValidationError(ErrorCodes.InvalidFormat, "salary_number", "Salary");

        // Act
        string result = FormatValidationErrorMessage(error);

        // Assert
        result.Should().Contain("please use system number format");
        result.Should().Contain("CSV file - please input number without commas");
    }

    [Fact]
    public void FormatValidationErrorMessage_WithInvalidOptions_ShouldReturnFormattedOptions()
    {
        // Arrange
        Dictionary<string, object?> context = new Dictionary<string, object?>
        {
            { "Options", new[] { "Option1", "Option2", "Option3" } }
        };
        ValidationError error = new ValidationError(ErrorCodes.InvalidOption, "memberType", "Member Type", context);

        // Act
        string result = FormatValidationErrorMessage(error);

        // Assert
        result.Should().Contain("Option1 or Option2 or Option3");
    }

    [Fact]
    public void FormatValidationErrorMessage_WithContextAwareMessage_ShouldIncludeRelevantContext()
    {
        // Arrange
        Dictionary<string, object?> context = new Dictionary<string, object?>
        {
            { "ExpectedValue", "YYYY-MM-DD" },
            { "ActualValue", "12/31/2023" }
        };
        ValidationError error = new ValidationError(ErrorCodes.InvalidFormat, "effectiveDate", "Effective Date", context);

        // Act
        string result = FormatValidationErrorMessage(error);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain("Effective Date");
    }

    [Fact]
    public void FormatValidationErrorMessage_WithUniqueViolation_ShouldIncludeFieldContext()
    {
        // Arrange
        Dictionary<string, object?> context = new Dictionary<string, object?>
        {
            { "DuplicateValue", "<EMAIL>" },
            { "ConflictingRows", new[] { 2, 5, 8 } }
        };
        ValidationError error = new ValidationError(ErrorCodes.UniqueViolation, "email", "Email", context);

        // Act
        string result = FormatValidationErrorMessage(error);

        // Assert
        result.Should().NotBeNullOrEmpty();
        result.Should().Contain("Email");
    }

    #endregion

    #region Context-Aware Error Message Tests

    [Fact]
    public void ValidationError_WithNumberFieldFormatError_ShouldProvideSpecificGuidance()
    {
        // Arrange & Act
        ValidationError error = new ValidationError(
            ErrorCodes.InvalidFormat,
            "premium_number",
            "Premium Amount");

        // Assert
        error.Message.Should().Contain("Premium Amount has an invalid format");
    }

    [Fact]
    public void ValidationError_WithMissingRequiredField_ShouldProvideSpecificMessage()
    {
        // Arrange & Act
        ValidationError error = new ValidationError(
            ErrorCodes.Required,
            "firstName",
            "First Name");

        // Assert
        error.Message.Should().Be("First Name is required");
    }

    [Fact]
    public void ValidationError_WithInvalidPlanId_ShouldIncludeAvailableOptions()
    {
        // Arrange
        Dictionary<string, object?> context = new Dictionary<string, object?>
        {
            { "AvailablePlans", new[] { "PLAN-001", "PLAN-002", "PLAN-003" } },
            { "InvalidValue", "INVALID-PLAN" }
        };

        // Act
        ValidationError error = new ValidationError(
            ErrorCodes.InvalidOption,
            "planId",
            "Plan ID",
            context);

        // Assert
        error.Message.Should().NotBeNullOrEmpty();
        error.Context.Should().ContainKey("AvailablePlans");
    }

    #endregion

    #region File Processing Error Tests

    [Fact]
    public async Task Handle_WithFileNotFound_ShouldReturnFailureResult()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup repository methods needed for validation workflow
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Setup file processing service to throw file not found exception
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new UploadFileNotFoundException(policy.Id, upload.Path));

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse("handler should return failure when file is not found");
        result.Errors.Should().ContainSingle(error => error.Code == "UPLOAD_FILE_NOT_FOUND");
    }

    [Fact]
    public async Task Handle_WithFileParsingFailure_ShouldReturnFileProcessingError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup repository methods needed for validation workflow
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Setup file processing service to return failure result
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Failure("Invalid CSV file format"));

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeFalse("handler should return failure when file processing fails");
        result.Errors.Should().ContainSingle(error => error.Code == "FILE_PROCESSING_FAILED");
        result.Errors[0].Message.Should().Contain("validation failed");
    }

    [Fact]
    public async Task Handle_WithSchemaRetrievalFailure_ShouldReturnFailureWithBadSchemaConfigError()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        // Setup schema provider to throw exception (schema not found)
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new BadSchemaConfigException($"Could not retrieve upload schema for policy {policy.Id} with product {policy.ProductId}"));

        // Setup file processing to succeed
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Errors.Should().ContainSingle(error => error.Code == ErrorCodes.BadSchemaConfig);
        result.Errors[0].Message.Should().Contain("Could not retrieve upload schema for policy");
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateValidMemberData()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "planId", "PLAN-001" },
            { "effectiveDate", DateTime.Today.ToString("yyyy-MM-dd") },
            { "firstName", "John" },
            { "lastName", "Doe" },
            { "name", "John Doe" },
            { "memberType", "employee" },
            { "dateOfBirth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "email", "<EMAIL>" },
            { "hkid", "A123456(7)" },
            { "staffNo", "STAFF001" }
        };

        return [memberData];
    }

    /// <summary>
    /// Simulates exception translation logic that would exist in the handler or specifications
    /// </summary>
    private List<ValidationError> CreateMemberIdValidationError(Exception exception, string memberId) => exception switch
    {
        MemberNotFoundException =>
            [new ValidationError("MEMBER_NOT_FOUND", "memberId", "Member ID")],
        PolicyMemberExistsException =>
            [new ValidationError("MEMBER_ID_TAKEN", "memberId", "Member ID")],
        _ =>
            [new ValidationError("VALIDATION_ERROR", "memberId", "Member ID")]
    };

    /// <summary>
    /// Simulates error message formatting logic that would exist in the domain or application layer
    /// </summary>
    private string FormatValidationErrorMessage(ValidationError error) => error.Code switch
    {
        ErrorCodes.InvalidFormat when error.PropertyPath?.Contains("number") == true =>
            $"{error.Message} - please use system number format. For CSV file - please input number without commas.",
        ErrorCodes.InvalidOption when error.Context?.ContainsKey("Options") == true =>
            $"{error.Message}. Valid options are: {string.Join(" or ", (string[])error.Context["Options"]!)}",
        _ => $"Row validation error: {error.Message}"
    };

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Setup default policy service responses
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default users service responses
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default member query service responses
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default uniqueness service responses
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup repository operations
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    #endregion
}