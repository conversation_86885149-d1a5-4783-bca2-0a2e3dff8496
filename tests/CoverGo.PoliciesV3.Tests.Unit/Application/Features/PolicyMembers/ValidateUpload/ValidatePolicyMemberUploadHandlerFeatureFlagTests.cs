using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Events;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using ProductId = CoverGo.Products.Client.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for feature flag control and business logic in ValidatePolicyMemberUploadHandler.
/// Covers UseTheSamePlanForEmployeeAndDependents and OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts
/// feature flags with SME/non-SME product type detection.
/// </summary>
public class ValidatePolicyMemberUploadHandlerFeatureFlagTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerFeatureFlagTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());

        // Use real UploadValidationOrchestrator instead of mock to enable dependent plan validation
        var realUploadValidationOrchestrator = new UploadValidationOrchestrator(Mock.Of<ILogger<UploadValidationOrchestrator>>());

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            realUploadValidationOrchestrator,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Create real CompleteUploadValidationSpecification for integration testing
        // Use real ValidationErrorAggregator instead of mock to get correct member counts
        var realErrorAggregator = new ValidationErrorAggregator(Mock.Of<ILogger<ValidationErrorAggregator>>());

        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            realErrorAggregator,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Create handler with real specifications
        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Feature Flag Control Tests

    // Feature flag and product data gathering tests moved to PolicyMemberValidationDataServiceTests

    #endregion

    #region Business Rule Validation Tests

    [Fact]
    public async Task Handle_WithValidDependentPlanMatching_ShouldPassValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create test data with dependent members that have SAME plan IDs as their primary members
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMatchingDependentPlans();

        SetupValidationScenario(memberData, policy, schema);

        // Setup feature flags to enable dependent plan validation
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup product service
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should pass when dependent plans match primary member plans");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    [Fact]
    public async Task Handle_WithInvalidDependentPlanMismatch_ShouldDetectValidationErrors()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create test data with dependent members that have DIFFERENT plan IDs than their primary members
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithDependentPlanMismatch();

        SetupValidationScenario(memberData, policy, schema);

        // Setup feature flags to enable dependent plan validation
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(true);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup product service
        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
        // rather than causing the handler to fail
        result.Value.PolicyMemberUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR, "upload should be marked as invalid due to plan mismatches");
        result.Value.PolicyMemberUpload.InvalidMembersCount.Should().BeGreaterThan(0, "should have detected invalid rows with plan mismatches");

        // Verify domain event was raised for validation completion
        result.Value.PolicyMemberUpload.DomainEvents.Should().ContainSingle(e => e is ValidationCompletedEvent);
        ValidationCompletedEvent validationEvent = result.Value.PolicyMemberUpload.DomainEvents.OfType<ValidationCompletedEvent>().First();
        validationEvent.InvalidCount.Should().BeGreaterThan(0, "should have detected invalid rows with plan mismatches");
        validationEvent.MemberErrors.Should().NotBeEmpty("should contain validation errors for plan mismatches");
        validationEvent.MemberErrors.Values.SelectMany(errors => errors).Should().Contain(error =>
            error.Code == ErrorCodes.DependentPlanMismatch, "should contain errors related to plan mismatches");
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().WithMemberCount(2).BuildPolicyMemberUpload();

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateValidMemberData()
    {
        MembersUploadFields memberData = MemberUploadTestDataBuilder.Create()
            .WithMemberCount(2)
            .WithDuplicatePercentage(0.0)
            .WithDependents(false)
            .WithExistingMembers(false)
            .BuildMembersUploadFields();

        return memberData.AsReadOnlyList().Select(m => m.Value).ToList();
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithDependentPlanMismatch()
    {
        // Create primary member with plan "PLAN-001"
        // Use field LABELS as keys (not field names) since LabelFieldNamesTransformer expects labels
        var primaryMember = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-001" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "John" },
            { "Last Name", "Doe" },
            { "Name", "John Doe" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "A123456(7)" },
            { "Staff Number", "STAFF001" }
        };

        // Create dependent member with different plan "PLAN-002" (should cause validation error)
        var dependentMember = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-002" }, // Different plan ID - should cause validation error
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "Jane" },
            { "Last Name", "Doe" },
            { "Name", "Jane Doe" },
            { "Member Type", "dependent" },
            { "Date of Birth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "B123456(8)" },
            { "Dependent Of", "1" } // References row 1 (primary member)
        };

        return [primaryMember, dependentMember];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithMatchingDependentPlans()
    {
        // Create primary member with plan "PLAN-001"
        // Use field LABELS as keys (not field names) since LabelFieldNamesTransformer expects labels
        var primaryMember = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-001" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "John" },
            { "Last Name", "Doe" },
            { "Name", "John Doe" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "A123456(7)" },
            { "Staff Number", "STAFF001" }
        };

        // Create dependent member with SAME plan "PLAN-001" (should pass validation)
        var dependentMember = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-001" }, // Same plan ID - should pass validation
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "Jane" },
            { "Last Name", "Doe" },
            { "Name", "Jane Doe" },
            { "Member Type", "dependent" },
            { "Date of Birth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "B123456(8)" },
            { "Dependent Of", "1" } // References row 1 (primary member)
        };

        return [primaryMember, dependentMember];
    }

    private void SetupValidationScenario(
        IReadOnlyList<IReadOnlyDictionary<string, string?>> memberData,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null)
    {
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto testPolicy = policy ?? CreateValidPolicy();
        PolicyMemberFieldsSchema testSchema = schema ?? CreateTestSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testPolicy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<PoliciesV3.Domain.Policies.ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testSchema);

        // Setup file processing service to return test member data
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        SetupDefaultMocks();
    }

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<PoliciesV3.Domain.Policies.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        // Setup default policy service responses
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default users service responses
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default member query service responses
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default uniqueness service responses
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup repository operations
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    #endregion
}