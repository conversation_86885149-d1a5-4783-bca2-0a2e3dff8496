using System.Diagnostics;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for service integration in ValidatePolicyMemberUploadHandler.
/// Covers Legacy Policy Service, Product Service, Member Repository, and File System Service integration.
/// </summary>
public class ValidatePolicyMemberUploadHandlerIntegrationTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerIntegrationTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());

        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
            It.IsAny<UploadWideValidationContext>(),
            It.IsAny<UploadValidationSpecs>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to return empty errors dictionary
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
            It.IsAny<int>(),
            It.IsAny<Func<int, Task<List<ValidationError>>>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Create real CompleteUploadValidationSpecification for integration testing
        var mockErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockErrorAggregator.Setup(x => x.AggregateResults(It.IsAny<List<BatchValidationResult>>(), It.IsAny<int>()))
            .Returns((List<BatchValidationResult> results, int totalCount) =>
            {
                // For integration tests, assume all members are valid when no errors are returned
                var allErrors = results.SelectMany(r => r.RowErrors).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                int totalInvalid = allErrors.Count;
                int totalValid = totalCount - totalInvalid;

                // Ensure we return a result with proper counts even when no validation errors
                if (totalValid == 0 && totalInvalid == 0 && totalCount > 0)
                {
                    totalValid = totalCount; // All members are valid if no errors
                }

                return BatchValidationResult.WithErrors(totalValid, totalInvalid, allErrors);
            });

        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            mockErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        // Create handler with real specifications
        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Legacy Policy Service Integration Tests

    [Fact]
    public async Task Handle_WithLegacyPolicyServiceIntegration_ShouldRetrievePolicySuccessfully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("legacy policy service integration should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify legacy policy service was called correctly
        _mockLegacyPolicyService.Verify(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithContractHolderPolicyRetrieval_ShouldCallLegacyServiceForContractHolderPolicies()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Setup contract holder policies
        List<string> contractHolderPolicies = ["POLICY-001", "POLICY-002", "POLICY-003"];
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(contractHolderPolicies);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateContractHolderPolicies());

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify contract holder policy retrieval
        _mockLegacyPolicyService.Verify(x => x.GetIdsByContractHolderId(policy.ContractHolderId, It.IsAny<CancellationToken>()), Times.Once);
        _mockLegacyPolicyService.Verify(x => x.GetPolicyDtosByIds(contractHolderPolicies, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithLegacyPolicyServiceTimeout_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        // Setup legacy policy service to timeout
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new TaskCanceledException("Policy service timeout"));

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() => _handler.Handle(command, CancellationToken.None));

        // Verify the service was called
        _mockLegacyPolicyService.Verify(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Product Service Integration Tests

    [Fact]
    public async Task Handle_WithProductServiceIntegration_ShouldRetrieveProductInformation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Setup product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("product service integration should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify product service calls
        _mockProductService.Verify(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockProductService.Verify(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithProductServiceFailure_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Setup product service to fail
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new HttpRequestException("Product service unavailable"));

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(() => _handler.Handle(command, CancellationToken.None));

        // Verify the service was called
        _mockProductService.Verify(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithProductServiceReturningNullPlans_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Setup product service to return null plans
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((IReadOnlyList<string>?)null);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should handle null plans gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Member Repository Integration Tests

    [Fact]
    public async Task Handle_WithMemberRepositoryIntegration_ShouldPerformBatchQueries()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUploadWithMemberCount(2); // 2 members in CreateMemberDataWithMemberIds()
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member IDs to trigger batch queries
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, upload);

        // Setup member repository responses
        Dictionary<string, PolicyMember?> existingMembers = new Dictionary<string, PolicyMember?>
            {
                { "MEMBER-001", null },
                { "MEMBER-002", CreateMockPolicyMember() }
            };

        Dictionary<string, List<PolicyMember>> validationStates = new Dictionary<string, List<PolicyMember>>
            {
                { "MEMBER-001", new List<PolicyMember>() },
                { "MEMBER-002", new List<PolicyMember> { CreateMockPolicyMember() } }
            };

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(
            It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingMembers);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(
            It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(validationStates);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("member repository integration should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify batch queries were performed
        _mockPolicyMemberQueryService.Verify(x => x.GetPolicyMembersBatchAsync(
            It.Is<List<string>>(list => list.Contains("MEMBER-001") && list.Contains("MEMBER-002")),
            It.IsAny<PolicyId>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);

        _mockPolicyMemberQueryService.Verify(x => x.GetMemberValidationStatesBatchAsync(
            It.Is<List<string>>(list => list.Contains("MEMBER-001") && list.Contains("MEMBER-002")),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithRepositoryOperations_ShouldManageUploadLifecycle()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify repository lifecycle operations
        _mockUploadRepository.Verify(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUploadRepository.Verify(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUploadRepository.Verify(x => x.CompleteValidationIfNotLockedAsync(
            It.IsAny<PolicyMemberUploadId>(),
            It.IsAny<PolicyMemberUploadStatus>(),
            It.IsAny<int>(),
            It.IsAny<int>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region File System Service Integration Tests

    [Fact]
    public async Task Handle_WithFileProcessingServiceIntegration_ShouldProcessFileSuccessfully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("file processing service integration should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify file processing service was called correctly
        _mockFileProcessingService.Verify(x => x.ProcessUploadFileAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithLargeFileProcessing_ShouldHandleEfficientlyWithBatching()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create large dataset to test batching efficiency
        List<IReadOnlyDictionary<string, string?>> largeDataset = CreateLargeMemberDataset(1000);

        SetupValidationScenario(largeDataset, policy, schema);

        // Act
        Stopwatch stopwatch = Stopwatch.StartNew();
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue("large file processing should complete successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify performance is acceptable for large datasets
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(5000, "large file processing should complete within 5 seconds");

        // Verify file processing was called once (not per member)
        _mockFileProcessingService.Verify(x => x.ProcessUploadFileAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithFileProcessingFailure_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(policy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(schema);

        // Setup file processing service to fail
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new IOException("File access denied"));

        // Act & Assert
        await Assert.ThrowsAsync<IOException>(() => _handler.Handle(command, CancellationToken.None));

        // Verify the service was called
        _mockFileProcessingService.Verify(x => x.ProcessUploadFileAsync(
            It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Schema Provider Integration Tests

    [Fact]
    public async Task Handle_WithSchemaProviderIntegration_ShouldRetrieveSchemaSuccessfully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("schema provider integration should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify schema provider was called correctly
        _mockSchemaProvider.Verify(x => x.GetMemberUploadSchema(
            It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithSchemaProviderForEndorsement_ShouldPassEndorsementId()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUploadWithEndorsement();

        // Create policy with matching endorsement ID
        PolicyDto policy = CreateValidPolicy();
        var endorsementDto = new EndorsementDto
        {
            Id = upload.EndorsementId!.Value.ToString(),
            Status = "DRAFT"
        };
        policy = policy with
        {
            Endorsements = [endorsementDto],
            ApprovedEndorsementIds = [upload.EndorsementId!.Value.ToString()]
        };

        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema, upload);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify schema provider was called with endorsement ID
        _mockSchemaProvider.Verify(x => x.GetMemberUploadSchema(
            It.IsAny<string?>(),
            It.IsAny<ProductId>(),
            It.Is<EndorsementId?>(id => id != null),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Users Service Integration Tests

    [Fact]
    public async Task Handle_WithUsersServiceIntegration_ShouldQueryIndividuals()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUploadWithMemberCount(2); // 2 members in CreateMemberDataWithMemberIds()
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member IDs to trigger users service queries
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, upload);

        // Setup users service response
        List<Individual> individuals =
        [
            CreateMockIndividual("MEMBER-001"),
                CreateMockIndividual("MEMBER-002")
        ];

        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("users service integration should work correctly");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify users service was called correctly
        _mockUsersService.Verify(x => x.QueryIndividuals(
            It.Is<QueryArgumentsOfIndividualWhere>(q =>
                q.Where != null &&
                q.Where.InternalCode_in != null &&
                q.Where.InternalCode_in.Contains("MEMBER-001") &&
                q.Where.InternalCode_in.Contains("MEMBER-002")),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithUsersServiceTimeout_ShouldHandleGracefully()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUploadWithMemberCount(2); // 2 members in CreateMemberDataWithMemberIds()
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member IDs to trigger users service queries
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, upload);

        // Setup users service to timeout
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new TaskCanceledException("Users service timeout"));

        // Act & Assert
        await Assert.ThrowsAsync<TaskCanceledException>(() => _handler.Handle(command, CancellationToken.None));

        // Verify the service was called
        _mockUsersService.Verify(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region Multi-Service Coordination Tests

    [Fact]
    public async Task Handle_WithAllServicesIntegration_ShouldCoordinateEfficiently()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        PolicyMemberUpload upload = CreateValidUploadWithMemberCount(2); // 2 members in CreateMemberDataWithMemberIds()
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, upload);

        // Setup all services with realistic responses
        SetupComprehensiveServiceResponses();

        // Act
        Stopwatch stopwatch = Stopwatch.StartNew();
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue("all services should coordinate successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify performance is acceptable with all service calls
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(2000, "multi-service coordination should complete within 2 seconds");

        // Verify all services were called
        VerifyAllServiceInteractions();
    }

    [Fact]
    public async Task Handle_WithServiceFailureRecovery_ShouldHandlePartialFailures()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();
        List<IReadOnlyDictionary<string, string?>> memberData = CreateValidMemberData();

        SetupValidationScenario(memberData, policy, schema);

        // Setup some services to fail gracefully
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((IReadOnlyList<string>?)null); // Graceful failure

        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]); // Empty result

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should handle partial service failures gracefully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
    }

    #endregion

    #region Mock Setup Methods

    private void SetupMemoryCacheMocks()
    {
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);
    }

    private void SetupFeatureFlagMocks()
    {
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);
    }

    private void SetupProductServiceMocks()
    {
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");
    }

    private void SetupPolicyServiceMocks()
    {
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    private void SetupUsersServiceMocks()
    {
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    private void SetupMemberQueryServiceMocks()
    {
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    private void SetupUniquenessServiceMocks()
    {
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);
    }

    private void SetupRepositoryMocks()
    {
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
            It.IsAny<PolicyMemberUploadId>(),
            It.IsAny<PolicyMemberUploadStatus>(),
            It.IsAny<int>(),
            It.IsAny<int>(),
            It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    private void SetupDefaultMocks()
    {
        SetupMemoryCacheMocks();
        SetupFeatureFlagMocks();
        SetupProductServiceMocks();
        SetupPolicyServiceMocks();
        SetupUsersServiceMocks();
        SetupMemberQueryServiceMocks();
        SetupUniquenessServiceMocks();
        SetupRepositoryMocks();
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().WithMemberCount(1).BuildPolicyMemberUpload();

    private PolicyMemberUpload CreateValidUploadWithEndorsement()
    {
        PolicyMemberUpload upload = MemberUploadTestDataBuilder.Create().WithMemberCount(1).BuildPolicyMemberUpload();
        // Manually set endorsement ID using reflection or create a new upload with endorsement
        var endorsementId = new EndorsementId(Guid.NewGuid());
        return PolicyMemberUpload.Create(
            upload.PolicyId,
            upload.Path,
            upload.MembersCount,
            endorsementId);
    }

    private PolicyMemberUpload CreateValidUploadWithMemberCount(int memberCount) => MemberUploadTestDataBuilder.Create().WithMemberCount(memberCount).BuildPolicyMemberUpload();

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateValidMemberData()
    {
        // Use field labels as keys (as expected by LabelFieldNamesTransformer)
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
            {
                { "Plan ID", "PLAN-001" },
                { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
                { "First Name", "John" },
                { "Last Name", "Doe" },
                { "Name", "John Doe" },
                { "Member Type", "employee" },
                { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "Email", "<EMAIL>" },
                { "HKID", "A123456(7)" },
                { "Staff Number", "STAFF001" }
            };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithMemberIds()
    {
        // Use field labels as keys (as expected by LabelFieldNamesTransformer)
        Dictionary<string, string?> member1 = new Dictionary<string, string?>
            {
                { "Member ID", "MEMBER-001" },
                { "Plan ID", "PLAN-001" },
                { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
                { "First Name", "John" },
                { "Last Name", "Doe" },
                { "Name", "John Doe" },
                { "Member Type", "employee" },
                { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
                { "Email", "<EMAIL>" },
                { "HKID", "A123456(7)" },
                { "Staff Number", "STAFF001" }
            };

        Dictionary<string, string?> member2 = new Dictionary<string, string?>
            {
                { "Member ID", "MEMBER-002" },
                { "Plan ID", "PLAN-002" },
                { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
                { "First Name", "Jane" },
                { "Last Name", "Smith" },
                { "Name", "Jane Smith" },
                { "Member Type", "employee" },
                { "Date of Birth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
                { "Email", "<EMAIL>" },
                { "HKID", "B123456(8)" },
                { "Staff Number", "STAFF002" }
            };

        return [member1, member2];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateLargeMemberDataset(int count)
    {
        List<IReadOnlyDictionary<string, string?>> dataset = [];

        for (int i = 1; i <= count; i++)
        {
            // Use field labels as keys (as expected by LabelFieldNamesTransformer)
            Dictionary<string, string?> member = new Dictionary<string, string?>
                {
                    { "Plan ID", $"PLAN-{(i % 3) + 1:D3}" },
                    { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
                    { "First Name", $"FirstName{i}" },
                    { "Last Name", $"LastName{i}" },
                    { "Name", $"FirstName{i} LastName{i}" },
                    { "Member Type", "employee" },
                    { "Date of Birth", DateTime.Today.AddYears(-30 - (i % 20)).ToString("yyyy-MM-dd") },
                    { "Email", $"member{i}@test.com" },
                    { "HKID", $"A{i:D6}({(i % 10)})" },
                    { "Staff Number", $"STAFF{i:D3}" }
                };

            dataset.Add(member);
        }

        return dataset;
    }

    private List<PolicyDto> CreateContractHolderPolicies() =>
        [
            MemberUploadTestDataBuilder.Create().BuildPolicyDto("POLICY-001"),
                MemberUploadTestDataBuilder.Create().BuildPolicyDto("POLICY-002"),
                MemberUploadTestDataBuilder.Create().BuildPolicyDto("POLICY-003")
        ];

    private Individual CreateMockIndividual(string internalCode) => new()
    {
        InternalCode = internalCode
    };

    private PolicyMember CreateMockPolicyMember() => PolicyMember.Create(
            PolicyId.New,
            "TEST-MEMBER",
            DateOnly.FromDateTime(DateTime.Today),
            null,
            "PLAN-001");

    private void SetupValidationScenario(
        IReadOnlyList<IReadOnlyDictionary<string, string?>> memberData,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        PolicyMemberUpload? upload = null)
    {
        PolicyMemberUpload testUpload = upload ?? CreateValidUpload();
        PolicyDto testPolicy = policy ?? CreateValidPolicy();
        PolicyMemberFieldsSchema testSchema = schema ?? CreateTestSchema();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testUpload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testPolicy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testSchema);

        // Setup file processing service to return test member data
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        SetupDefaultMocks();
    }

    private void SetupComprehensiveServiceResponses()
    {
        // Setup product service
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Setup legacy policy service
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["POLICY-001", "POLICY-002"]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateContractHolderPolicies());

        // Setup users service
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([CreateMockIndividual("MEMBER-001"), CreateMockIndividual("MEMBER-002")]);

        // Setup member query service
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new Dictionary<string, PolicyMember?> { { "MEMBER-001", null }, { "MEMBER-002", null } });

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default mocks for repository operations
        SetupDefaultMocks();
    }

    private void VerifyAllServiceInteractions()
    {
        // Verify legacy policy service interactions
        _mockLegacyPolicyService.Verify(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockLegacyPolicyService.Verify(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);

        // Verify product service interactions
        _mockProductService.Verify(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockProductService.Verify(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()), Times.Once);

        // Verify file processing service interactions
        _mockFileProcessingService.Verify(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);

        // Verify schema provider interactions
        _mockSchemaProvider.Verify(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()), Times.Once);

        // Verify users service interactions
        _mockUsersService.Verify(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()), Times.Once);

        // Verify repository interactions
        _mockUploadRepository.Verify(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUploadRepository.Verify(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUploadRepository.Verify(x => x.CompleteValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<PolicyMemberUploadStatus>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion
}