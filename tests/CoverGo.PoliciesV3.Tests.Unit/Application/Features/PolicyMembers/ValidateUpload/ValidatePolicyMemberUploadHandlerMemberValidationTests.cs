using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.CustomFields.FieldTypes;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Products;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

public class ValidatePolicyMemberUploadHandlerMemberValidationTests
{
    #region Test Infrastructure

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    // Specification mocks
    private readonly Mock<CompleteUploadValidationSpecification> _mockCompleteValidationSpec;

    private readonly TenantId _tenantId;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerMemberValidationTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockUsersService = new Mock<IUsersService>();
        _mockCompleteValidationSpec = new Mock<CompleteUploadValidationSpecification>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup configuration
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(s => s.Value).Returns("10");
        _mockConfiguration.Setup(c => c.GetSection("ValidationSettings:MaxConcurrentValidations")).Returns(mockSection.Object);

        // Initialize specification mocks with proper logger mocks

        // Create real instances of specifications with mock dependencies
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());
        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            Mock.Of<IUploadValidationOrchestrator>(),
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(Mock.Of<IPolicyMemberUniquenessService>(), Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(Mock.Of<IPolicyMemberQueryService>(), Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            Mock.Of<IConcurrentMemberProcessor>(),
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        _mockCompleteValidationSpec = new Mock<CompleteUploadValidationSpecification>(
            uploadWideSpec,
            individualMemberSpec,
            Mock.Of<IValidationErrorAggregator>(),
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        // Setup the ValidateAsync method to return a successful result by default
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(ValidationOrchestrationResult.Success(0, 0, []));

        _tenantId = new TenantId("test-tenant");

        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        // Initialize handler
        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _mockCompleteValidationSpec.Object,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        // Initialize fixture
        _fixture = new Fixture();
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));

        // Set up default mock behaviors
        SetupDefaultMockBehaviors();
    }

    private void SetupDefaultMockBehaviors()
    {
        // Default upload repository behavior
        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(PolicyMemberUpload)!);

        // Default policy service behavior
        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateValidPolicy());

        // Default schema provider behavior
        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateValidSchema());

        // Default product service behavior
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["plan-1"]);

        // Default feature manager behavior
        _mockFeatureManager.Setup(x => x.IsEnabled(It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync(false);

        // Default file processing service behavior
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success([], 0));

        // Default users service behavior
        _mockUsersService.Setup(x => x.GetMemberIdsWithoutExistingIndividual(It.IsAny<HashSet<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup QueryIndividuals to return empty list by default
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Default legacy policy service behavior
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["default-policy-id"]);

        // Default policy member query service behavior
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(It.IsAny<string>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember?)null);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Default uniqueness service behavior
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(It.IsAny<PolicyId>(), It.IsAny<string>(), It.IsAny<PolicyMemberId?>(), It.IsAny<Dictionary<string, object>>(), It.IsAny<List<string>>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Repository operations required by the handler
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    #endregion

    #region Test Data Helpers

    private static PolicyMemberUpload CreateValidUpload() => PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 100);

    private static PolicyDto CreateValidPolicy()
    {
        string endorsementId = Guid.NewGuid().ToString();
        return new PolicyDto
        {
            Id = Guid.NewGuid().ToString(),
            ContractHolderId = Guid.NewGuid().ToString(),
            ProductId = new ProductIdDto { Plan = "plan", Type = "type", Version = "version" },
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(-30)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            ApprovedEndorsementIds = [endorsementId],
            Endorsements =
            [
                new() { Id = endorsementId, Status = "APPROVED" }
            ]
        };
    }

    private static PolicyMemberFieldsSchema CreateValidSchema()
    {
        var fields = new List<PolicyMemberFieldDefinition>
        {
            new() { Name = "name", Label = "Name", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "email", Label = "Email", Type = new StringFieldType(), IsRequired = true, IsUnique = true },
            new() { Name = "memberId", Label = "Member ID", Type = new StringFieldType(), IsRequired = false },
            new() { Name = "memberType", Label = "Member Type", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "dependentOf", Label = "Dependent Of", Type = new StringFieldType(), IsRequired = false },
            new() { Name = "planId", Label = "Plan ID", Type = new StringFieldType(), IsRequired = true },
            new() { Name = "effectiveDate", Label = "Effective Date", Type = new DateFieldType(), IsRequired = true }
        };

        return new PolicyMemberFieldsSchema(fields);
    }

    #endregion

    #region Member Validation Tests

    [Fact]
    public async Task ValidateUpload_WithValidDependentMember_ShouldSucceed()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["Member Type"] = "dependent",
                ["Dependent Of"] = "non-existent-member",
                ["Email"] = "<EMAIL>",
                ["Plan ID"] = "plan-1",
                ["Effective Date"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["First Name"] = "Dependent",
                ["Last Name"] = "Member",
                ["Name"] = "Dependent Member",
                ["Date of Birth"] = DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd")
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup primary member exists in policy
        var primaryMember = PolicyMember.Create(
            new PolicyId(Guid.Parse(policy.Id)),
            "primary-member-1",
            DateOnly.FromDateTime(DateTime.Today),
            DateOnly.FromDateTime(DateTime.Today.AddYears(1)),
            "plan-1");

        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(
                "primary-member-1",
                new PolicyId(Guid.Parse(policy.Id)),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(primaryMember);

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().NotBeNull();
    }

    [Fact]
    public async Task ValidateUpload_WithDependentMemberMissingPrimaryMember_ShouldSucceedWithValidationErrors()
    {
        // Arrange
        PolicyMemberUpload upload = PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 1); // 1 member to match test data
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["Member Type"] = "dependent",
                ["Dependent Of"] = "non-existent-primary",
                ["Email"] = "<EMAIL>",
                ["Plan ID"] = "plan-1",
                ["Effective Date"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["First Name"] = "Dependent",
                ["Last Name"] = "Member",
                ["Name"] = "Dependent Member",
                ["Date of Birth"] = DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd")
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup primary member does not exist
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMemberCurrentStateAsync(
                "non-existent-primary",
                new PolicyId(Guid.Parse(policy.Id)),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMember?)null);

        // Setup validation to return business validation errors (non-blocking)
        var validationErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] =
            [
                new ValidationError(ErrorCodes.MemberNotFound, "dependentOf", "Dependent Of")
            ]
        };
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationOrchestrationResult
            {
                IsSuccessful = false,
                ValidMembersCount = 0,
                InvalidMembersCount = 1,
                MemberErrors = validationErrors
            });

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Business validation errors are non-blocking - handler should succeed
        result.IsSuccess.Should().BeTrue();

        // Validation errors should be stored in the upload for user feedback
        PolicyMemberUpload responseUpload = result.Value.PolicyMemberUpload;
        responseUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR);
        responseUpload.InvalidMembersCount.Should().Be(1);
        responseUpload.ValidMembersCount.Should().Be(0);
        responseUpload.ValidationErrors.Should().NotBeEmpty();
        responseUpload.ValidationErrors.Should().Contain(e => e.Code == ErrorCodes.MemberNotFound);
    }

    [Fact]
    public async Task ValidateUpload_WithInvalidMemberIdBusinessRules_ShouldSucceedWithValidationErrors()
    {
        // Arrange
        PolicyMemberUpload upload = PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 1); // 1 member to match test data
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["Member Type"] = "employee",
                ["Member ID"] = "invalid-member-id",
                ["Email"] = "<EMAIL>",
                ["Plan ID"] = "plan-1",
                ["Effective Date"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["First Name"] = "Employee",
                ["Last Name"] = "Member",
                ["Name"] = "Employee Member",
                ["Date of Birth"] = DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd")
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup member ID validation to fail (member not found)
        _mockUsersService.Setup(x => x.GetMemberIdsWithoutExistingIndividual(
                It.Is<HashSet<string>>(set => set.Contains("invalid-member-id")),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["invalid-member-id"]); // Return the invalid member ID

        // Setup validation to return business validation errors (non-blocking)
        var validationErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] =
            [
                new ValidationError("MEMBER_NOT_FOUND", "memberId", "Member ID")
            ]
        };
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationOrchestrationResult
            {
                IsSuccessful = false,
                ValidMembersCount = 0,
                InvalidMembersCount = 1,
                MemberErrors = validationErrors
            });

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Business validation errors are non-blocking - handler should succeed
        result.IsSuccess.Should().BeTrue();

        // Validation errors should be stored in the upload for user feedback
        PolicyMemberUpload responseUpload = result.Value.PolicyMemberUpload;
        responseUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR);
        responseUpload.InvalidMembersCount.Should().Be(1);
        responseUpload.ValidMembersCount.Should().Be(0);
        responseUpload.ValidationErrors.Should().NotBeEmpty();
        responseUpload.ValidationErrors.Should().Contain(e => e.Code == "MEMBER_NOT_FOUND");
    }

    [Fact]
    public async Task ValidateUpload_WithInvalidFieldsSchema_ShouldSucceedWithValidationErrors()
    {
        // Arrange
        PolicyMemberUpload upload = PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 1); // 1 member to match test data
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["memberType"] = "employee",
                ["email"] = "", // Invalid: required field is empty
                ["planId"] = "plan-1",
                ["effectiveDate"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["firstName"] = "Employee",
                ["lastName"] = "Member"
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup validation to return business validation errors (non-blocking)
        var validationErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] =
            [
                new ValidationError(ErrorCodes.Required, "email", "Email")
            ]
        };
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationOrchestrationResult
            {
                IsSuccessful = false,
                ValidMembersCount = 0,
                InvalidMembersCount = 1,
                MemberErrors = validationErrors
            });

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Business validation errors are non-blocking - handler should succeed
        result.IsSuccess.Should().BeTrue();

        // Validation errors should be stored in the upload for user feedback
        PolicyMemberUpload responseUpload = result.Value.PolicyMemberUpload;
        responseUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR);
        responseUpload.InvalidMembersCount.Should().Be(1);
        responseUpload.ValidMembersCount.Should().Be(0);
        responseUpload.ValidationErrors.Should().NotBeEmpty();
        responseUpload.ValidationErrors.Should().Contain(e => e.Code == ErrorCodes.Required);
    }

    [Fact]
    public void DateFieldType_ValidateField_WithInvalidDate_ShouldFail()
    {
        // Arrange
        var dateFieldType = new DateFieldType();
        var field = new PolicyMemberFieldDefinition
        {
            Name = "effectiveDate",
            Label = "Effective Date",
            Type = dateFieldType,
            IsRequired = true
        };

        // Act
        Result<object?> result = dateFieldType.ValidateField("invalid-date", field);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Errors.Should().NotBeEmpty();
    }

    [Fact]
    public async Task ValidateUpload_WithInvalidEffectiveDate_ShouldSucceedWithValidationErrors()
    {
        // Arrange
        PolicyMemberUpload upload = PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 1); // 1 member to match test data
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["Member Type"] = "employee",
                ["Email"] = "<EMAIL>",
                ["Plan ID"] = "plan-1",
                ["Effective Date"] = "invalid-date", // Invalid date format
                ["Name"] = "Employee Member" // Required field
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup validation to return business validation errors (non-blocking)
        var validationErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] =
            [
                new ValidationError(ErrorCodes.InvalidFormat, "effectiveDate", "Effective Date")
            ]
        };
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationOrchestrationResult
            {
                IsSuccessful = false,
                ValidMembersCount = 0,
                InvalidMembersCount = 1,
                MemberErrors = validationErrors
            });

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Business validation errors are non-blocking - handler should succeed
        result.IsSuccess.Should().BeTrue();

        // Validation errors should be stored in the upload for user feedback
        PolicyMemberUpload responseUpload = result.Value.PolicyMemberUpload;
        responseUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR);
        responseUpload.InvalidMembersCount.Should().Be(1);
        responseUpload.ValidMembersCount.Should().Be(0);
        responseUpload.ValidationErrors.Should().NotBeEmpty();
        responseUpload.ValidationErrors.Should().Contain(e => e.Code == ErrorCodes.InvalidFormat);
    }

    [Fact]
    public async Task ValidateUpload_WithInvalidPlanId_ShouldSucceedWithValidationErrors()
    {
        // Arrange
        PolicyMemberUpload upload = PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 1); // 1 member to match test data
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["Member Type"] = "employee",
                ["Email"] = "<EMAIL>",
                ["Plan ID"] = "invalid-plan-id", // Plan ID not in available plans
                ["Effective Date"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["First Name"] = "Employee",
                ["Last Name"] = "Member",
                ["Name"] = "Employee Member",
                ["Date of Birth"] = DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd")
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup available plans (doesn't include invalid-plan-id)
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["plan-1"]);

        // Setup validation to return business validation errors (non-blocking)
        var validationErrors = new Dictionary<int, List<ValidationError>>
        {
            [0] =
            [
                new ValidationError(ErrorCodes.InvalidPlanId, "planId", "Plan ID")
            ]
        };
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationOrchestrationResult
            {
                IsSuccessful = false,
                ValidMembersCount = 0,
                InvalidMembersCount = 1,
                MemberErrors = validationErrors
            });

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Business validation errors are non-blocking - handler should succeed
        result.IsSuccess.Should().BeTrue();

        // Validation errors should be stored in the upload for user feedback
        PolicyMemberUpload responseUpload = result.Value.PolicyMemberUpload;
        responseUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR);
        responseUpload.InvalidMembersCount.Should().Be(1);
        responseUpload.ValidMembersCount.Should().Be(0);
        responseUpload.ValidationErrors.Should().NotBeEmpty();
        responseUpload.ValidationErrors.Should().Contain(e => e.Code == ErrorCodes.InvalidPlanId);
    }

    [Fact]
    public async Task ValidateUpload_WithDependentAndEmployeeOnDifferentPlans_ShouldFail()
    {
        // Arrange
        // Create upload with correct member count (2) to match test data
        PolicyMemberUpload upload = PolicyMemberUpload.Create(PolicyId.New, "uploads/test-file.csv", 2);
        PolicyDto policy = CreateValidPolicy();

        var memberData = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?>
            {
                ["Member Type"] = "employee",
                ["Member ID"] = "emp-1",
                ["Plan ID"] = "plan-1",
                ["Email"] = "<EMAIL>",
                ["Name"] = "Employee 1",
                ["Effective Date"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["Date of Birth"] = DateTime.Today.AddYears(-35).ToString("yyyy-MM-dd")
            },
            new Dictionary<string, string?>
            {
                ["Member Type"] = "dependent",
                ["Dependent Of"] = "emp-1",
                ["Plan ID"] = "plan-2",
                ["Email"] = "<EMAIL>",
                ["Name"] = "Dependent 1",
                ["Effective Date"] = DateTime.Today.ToString("yyyy-MM-dd"),
                ["Date of Birth"] = DateTime.Today.AddYears(-10).ToString("yyyy-MM-dd")
            }
        };

        // Setup mocks
        _mockUploadRepository.Setup(x => x.FindByIdAsync(upload.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // Setup available plans
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["plan-1", "plan-2"]);

        // Setup validation to return success with dependent plan mismatch errors
        var validationErrors = new Dictionary<int, List<ValidationError>>
        {
            [1] =
            [
                new ValidationError(ErrorCodes.DependentPlanMismatch, "planId", "Plan ID")
            ]
        };
        _mockCompleteValidationSpec.Setup(x => x.ProcessUploadComplianceAsync(It.IsAny<CompleteValidationContext>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new ValidationOrchestrationResult
            {
                IsSuccessful = true,
                ValidMembersCount = 1,
                InvalidMembersCount = 1,
                MemberErrors = validationErrors
            });

        ValidatePolicyMemberUploadCommand command = new()
        {
            PolicyId = Guid.Parse(policy.Id),
            UploadId = upload.Id.Value
        };

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue("handler should succeed and capture validation errors in upload");
        result.Value.PolicyMemberUpload.Should().NotBeNull();
        result.Value.PolicyMemberUpload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR, "upload should be marked as invalid due to plan mismatches");
        result.Value.PolicyMemberUpload.ValidationErrors.Should().NotBeEmpty("should contain validation errors for plan mismatches");
        result.Value.PolicyMemberUpload.ValidationErrors.Should().Contain(error =>
            error.Code == ErrorCodes.DependentPlanMismatch, "should contain errors related to plan mismatches");
    }

    #endregion
}