using System.Diagnostics;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads.ValidateUpload;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using CoverGo.PoliciesV3.Tests.Unit.TestData;
using CoverGo.Users.Client;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.Features.PolicyMembers.ValidateUpload;

/// <summary>
/// Tests for multi-scope validation in ValidatePolicyMemberUploadHandler.
/// Covers tenant, policy, contract holder, and user scope validation scenarios.
/// </summary>
public class ValidatePolicyMemberUploadHandlerMultiScopeTests
{
    #region Test Setup and Dependencies

    private readonly Mock<IPolicyMemberUploadRepository> _mockUploadRepository;
    private readonly Mock<ILegacyPolicyService> _mockLegacyPolicyService;
    private readonly Mock<IFileProcessingService> _mockFileProcessingService;
    private readonly Mock<IPolicyMemberFieldsSchemaProvider> _mockSchemaProvider;
    private readonly Mock<IPolicyMemberQueryService> _mockPolicyMemberQueryService;
    private readonly Mock<IPolicyMemberUniquenessService> _mockPolicyMemberUniquenessService;
    private readonly Mock<IProductService> _mockProductService;
    private readonly Mock<IMultiTenantFeatureManager> _mockFeatureManager;
    private readonly Mock<IUsersService> _mockUsersService;
    private readonly Mock<ILogger<ValidatePolicyMemberUploadHandler>> _mockLogger;
    private readonly Mock<IMemoryCache> _mockMemoryCache;

    private readonly CompleteUploadValidationSpecification _realCompleteValidationSpec;
    private readonly ValidatePolicyMemberUploadHandler _handler;
    private readonly TenantId _tenantId;
    private readonly Fixture _fixture;

    public ValidatePolicyMemberUploadHandlerMultiScopeTests()
    {
        // Initialize mocks
        _mockUploadRepository = new Mock<IPolicyMemberUploadRepository>();
        _mockLegacyPolicyService = new Mock<ILegacyPolicyService>();
        _mockFileProcessingService = new Mock<IFileProcessingService>();
        _mockSchemaProvider = new Mock<IPolicyMemberFieldsSchemaProvider>();
        _mockPolicyMemberQueryService = new Mock<IPolicyMemberQueryService>();
        _mockPolicyMemberUniquenessService = new Mock<IPolicyMemberUniquenessService>();
        _mockProductService = new Mock<IProductService>();
        _mockFeatureManager = new Mock<IMultiTenantFeatureManager>();
        _mockUsersService = new Mock<IUsersService>();
        _mockLogger = new Mock<ILogger<ValidatePolicyMemberUploadHandler>>();
        _mockMemoryCache = new Mock<IMemoryCache>();

        // Create real instances of specifications for integration testing
        var uploadUniqueEmailsSpec = new UploadMustHaveUniqueEmailsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueEmailsSpecification>>());
        var uploadUniqueIdSpec = new UploadMustHaveUniqueIdentificationSpecification(Mock.Of<ILogger<UploadMustHaveUniqueIdentificationSpecification>>());
        var uploadUniqueMemberIdsSpec = new UploadMustHaveUniqueMemberIdsSpecification(Mock.Of<ILogger<UploadMustHaveUniqueMemberIdsSpecification>>());
        var dependentPlanSpec = new DependentAndEmployeeMustBeOnSamePlanSpecification(Mock.Of<ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification>>());

        // Setup mock for IUploadValidationOrchestrator to return empty errors dictionary
        var mockUploadValidationOrchestrator = new Mock<IUploadValidationOrchestrator>();
        mockUploadValidationOrchestrator.Setup(x => x.ExecuteUploadValidationsAsync(
                It.IsAny<UploadWideValidationContext>(),
                It.IsAny<UploadValidationSpecs>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        var uploadWideSpec = new UploadWideValidationSpecification(
            uploadUniqueEmailsSpec,
            uploadUniqueIdSpec,
            uploadUniqueMemberIdsSpec,
            dependentPlanSpec,
            mockUploadValidationOrchestrator.Object,
            Mock.Of<ILogger<UploadWideValidationSpecification>>());

        var memberUniqueEmailSpec = new MemberMustHaveUniqueEmailSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueEmailSpecification>>());
        var memberUniqueHkidSpec = new MemberMustHaveUniqueHKIDSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueHKIDSpecification>>());
        var memberUniquePassportSpec = new MemberMustHaveUniquePassportSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniquePassportSpecification>>());
        var memberUniqueStaffSpec = new MemberMustHaveUniqueStaffNumberSpecification(_mockPolicyMemberUniquenessService.Object, Mock.Of<ILogger<MemberMustHaveUniqueStaffNumberSpecification>>());
        var memberIdBusinessRulesSpec = new MemberIdMustFollowBusinessRulesSpecification(Mock.Of<ILogger<MemberIdMustFollowBusinessRulesSpecification>>());
        var memberFieldsSchemaSpec = new MemberFieldsMustMatchSchemaSpecification(Mock.Of<ILogger<MemberFieldsMustMatchSchemaSpecification>>());
        var memberEffectiveDateSpec = new MemberEffectiveDateMustBeValidSpecification(Mock.Of<ILogger<MemberEffectiveDateMustBeValidSpecification>>());
        var dependentValidationSpec = new DependentMustHaveValidPrimaryMemberSpecification(_mockPolicyMemberQueryService.Object, Mock.Of<ILogger<DependentMustHaveValidPrimaryMemberSpecification>>());
        var memberValidPlanIdSpec = new MemberMustHaveValidPlanIdSpecification(Mock.Of<ILogger<MemberMustHaveValidPlanIdSpecification>>());

        // Setup mock for IConcurrentMemberProcessor to actually call the validation function
        var mockConcurrentMemberProcessor = new Mock<IConcurrentMemberProcessor>();
        mockConcurrentMemberProcessor.Setup(x => x.ProcessMembersAsync(
                It.IsAny<int>(),
                It.IsAny<Func<int, Task<List<ValidationError>>>>(),
                It.IsAny<CancellationToken>()))
            .Returns<int, Func<int, Task<List<ValidationError>>>, CancellationToken>(async (memberCount, validationFunc, _) =>
            {
                var results = new Dictionary<int, List<ValidationError>>();
                for (int i = 0; i < memberCount; i++)
                {
                    List<ValidationError> errors = await validationFunc(i);
                    if (errors.Count > 0)
                    {
                        results[i] = errors;
                    }
                }
                return results;
            });

        var individualMemberSpec = new IndividualMemberValidationSpecification(
            memberUniqueEmailSpec,
            memberUniqueHkidSpec,
            memberUniquePassportSpec,
            memberUniqueStaffSpec,
            memberIdBusinessRulesSpec,
            memberFieldsSchemaSpec,
            memberEffectiveDateSpec,
            dependentValidationSpec,
            memberValidPlanIdSpec,
            mockConcurrentMemberProcessor.Object,
            Mock.Of<ILogger<IndividualMemberValidationSpecification>>());

        // Setup mock for IValidationErrorAggregator to return a valid result
        var mockValidationErrorAggregator = new Mock<IValidationErrorAggregator>();
        mockValidationErrorAggregator.Setup(x => x.AggregateResults(
                It.IsAny<List<BatchValidationResult>>(),
                It.IsAny<int>()))
            .Returns<List<BatchValidationResult>, int>((_, memberCount) => new BatchValidationResult
            {
                ValidCount = memberCount, // Return the actual member count as valid
                InvalidCount = 0,
                RowErrors = []
            });

        // Create real CompleteUploadValidationSpecification for integration testing
        _realCompleteValidationSpec = new CompleteUploadValidationSpecification(
            uploadWideSpec,
            individualMemberSpec,
            mockValidationErrorAggregator.Object,
            Mock.Of<ILogger<CompleteUploadValidationSpecification>>());

        _tenantId = new TenantId(Guid.NewGuid().ToString());
        _fixture = new Fixture();

        // Create handler with real specifications
        // Create mock for PolicyMemberValidationDataService
        var mockValidationDataService = new Mock<PolicyMemberValidationDataService>(
            _mockLegacyPolicyService.Object,
            _mockProductService.Object,
            _mockFeatureManager.Object,
            _tenantId,
            _mockSchemaProvider.Object,
            new Mock<ILogger<PolicyMemberValidationDataService>>().Object);

        _handler = new ValidatePolicyMemberUploadHandler(
            _mockUploadRepository.Object,
            _mockLegacyPolicyService.Object,
            _realCompleteValidationSpec,
            _mockUsersService.Object,
            _mockPolicyMemberQueryService.Object,
            _mockFileProcessingService.Object,
            _mockLogger.Object,
            mockValidationDataService.Object);

        SetupDefaultMocks();
    }

    #endregion

    #region Tenant Scope Validation Tests

    [Fact]
    public async Task Handle_WithTenantScopeUniquenessValidation_ShouldCallTenantScopeService()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that require tenant scope validation
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup tenant scope uniqueness service to return no conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("tenant scope validation should complete successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify tenant scope uniqueness service was called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithTenantScopeUniquenessViolation_ShouldDetectConflicts()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that will conflict at tenant scope
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup tenant scope uniqueness service to return conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["email", "hkid"]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    [Fact]
    public async Task Handle_WithTenantScopeValidationForMultipleMembers_ShouldBatchValidate()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create multiple members to test batch validation
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMultipleMembersWithUniqueFields();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup tenant scope uniqueness service
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();

        // Verify tenant scope validation was called for each member
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeast(memberData.Count));
    }

    #endregion

    #region Policy Scope Validation Tests

    [Fact]
    public async Task Handle_WithPolicyScopeUniquenessValidation_ShouldCallPolicyScopeService()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that require policy scope validation
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup policy scope uniqueness service to return no conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("policy scope validation should complete successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify policy scope uniqueness service was called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithPolicyScopeUniquenessViolation_ShouldDetectConflicts()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that will conflict at policy scope
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup policy scope uniqueness service to return conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["staffNo"]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    #endregion

    #region Contract Holder Scope Validation Tests

    [Fact]
    public async Task Handle_WithContractHolderScopeValidation_ShouldCallContractHolderScopeService()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that require contract holder scope validation
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        // Setup contract holder policies with valid GUIDs
        List<string> contractHolderPolicies =
        [
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString()
        ];

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create(
            contractHolderPolicyIds: contractHolderPolicies));
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(contractHolderPolicies);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateContractHolderPolicies());

        // Setup contract holder scope uniqueness service
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("contract holder scope validation should complete successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify that tenant and policy scope validations were called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);

        _mockPolicyMemberUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);

        // Verify contract holder scope validation was called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);
    }

    [Fact]
    public async Task Handle_WithContractHolderScopeUniquenessViolation_ShouldDetectConflicts()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that will conflict at contract holder scope
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        // Setup contract holder policies with valid GUIDs
        List<string> contractHolderPolicies =
        [
            Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString()
        ];

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create(
            contractHolderPolicyIds: contractHolderPolicies));
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(contractHolderPolicies);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateContractHolderPolicies());

        // Setup contract holder scope uniqueness service to return conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["hkid", "staffNo"]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    [Fact]
    public async Task Handle_WithEmptyContractHolderPolicies_ShouldSkipContractHolderScopeValidation()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with fields that require contract holder scope validation
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithUniqueFields();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup empty contract holder policies
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("validation should complete successfully when no contract holder policies exist");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify contract holder scope uniqueness service was not called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    #endregion

    #region User Scope Validation Tests

    [Fact]
    public async Task Handle_WithUserScopeValidation_ShouldQueryUsersService()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member IDs to trigger user scope validation
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup users service response
        List<Individual> individuals =
        [
            CreateMockIndividual("MEMBER-001"),
            CreateMockIndividual("MEMBER-002")
        ];

        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("user scope validation should complete successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify users service was called for user scope validation
        _mockUsersService.Verify(x => x.QueryIndividuals(
            It.Is<QueryArgumentsOfIndividualWhere>(q =>
                q.Where != null &&
                q.Where.InternalCode_in != null &&
                q.Where.InternalCode_in.Contains("MEMBER-001") &&
                q.Where.InternalCode_in.Contains("MEMBER-002")),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithUserScopeValidationForNonExistentUsers_ShouldDetectMissingUsers()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with member IDs that don't exist in users service
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup users service to return empty results (users don't exist)
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors should be captured in the upload's validation results
    }

    [Fact]
    public async Task Handle_WithUserScopeValidationForPartiallyExistingUsers_ShouldDetectMissingUsers()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data with multiple member IDs
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup users service to return only some users (partial existence)
        List<Individual> individuals =
        [
            CreateMockIndividual("MEMBER-001")
            // MEMBER-002 is missing
        ];

        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(individuals);

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with partial user validation errors");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors for missing users should be captured in the upload's validation results
    }

    #endregion

    #region Multi-Scope Coordination Tests

    [Fact]
    public async Task Handle_WithAllScopeValidations_ShouldCoordinateEfficiently()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data that triggers all scope validations
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create(
            contractHolderPolicyIds: [Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]));

        // Setup all scope validation services
        SetupAllScopeValidationServices();

        // Act
        Stopwatch stopwatch = Stopwatch.StartNew();
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);
        stopwatch.Stop();

        // Assert
        result.IsSuccess.Should().BeTrue("all scope validations should coordinate successfully");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // Verify performance is acceptable with all scope validations
        stopwatch.ElapsedMilliseconds.Should().BeLessThan(3000, "multi-scope validation should complete within 3 seconds");

        // Verify all scope validations were called
        VerifyAllScopeValidationInteractions();
    }

    [Fact]
    public async Task Handle_WithMixedScopeValidationResults_ShouldAggregateErrorsCorrectly()
    {
        // Arrange
        ValidatePolicyMemberUploadCommand command = CreateValidCommand();
        CreateValidUpload();
        PolicyDto policy = CreateValidPolicy();
        PolicyMemberFieldsSchema schema = CreateTestSchema();

        // Create member data that triggers all scope validations
        List<IReadOnlyDictionary<string, string?>> memberData = CreateMemberDataWithMemberIds();

        SetupValidationScenario(memberData, policy, schema, ResolvedValidationDataTestDataBuilder.Create());

        // Setup mixed scope validation results (some pass, some fail)
        SetupMixedScopeValidationResults();

        // Act
        Result<ValidatePolicyMemberUploadResponse> result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue("handler should complete successfully even with mixed validation results");
        result.Value.PolicyMemberUpload.Should().NotBeNull();

        // The validation errors from different scopes should be properly aggregated
    }

    #endregion

    #region Helper Methods

    private ValidatePolicyMemberUploadCommand CreateValidCommand() => new()
    {
        PolicyId = Guid.NewGuid(),
        UploadId = Guid.NewGuid()
    };

    private PolicyMemberUpload CreateValidUpload() => MemberUploadTestDataBuilder.Create().BuildPolicyMemberUpload();

    private PolicyDto CreateValidPolicy() => MemberUploadTestDataBuilder.Create().BuildPolicyDto();

    private PolicyMemberFieldsSchema CreateTestSchema() => MemberUploadTestDataBuilder.Create().BuildSchema();

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithUniqueFields()
    {
        Dictionary<string, string?> memberData = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-001" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "John" },
            { "Last Name", "Doe" },
            { "Name", "John Doe" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "A123456(7)" },
            { "Staff Number", "STAFF001" },
            { "Passport Number", "P123456789" }
        };

        return [memberData];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMultipleMembersWithUniqueFields()
    {
        Dictionary<string, string?> member1 = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-001" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "John" },
            { "Last Name", "Doe" },
            { "Name", "John Doe" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "A123456(7)" },
            { "Staff Number", "STAFF001" }
        };

        Dictionary<string, string?> member2 = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-002" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "Jane" },
            { "Last Name", "Smith" },
            { "Name", "Jane Smith" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "B123456(8)" },
            { "Staff Number", "STAFF002" }
        };

        Dictionary<string, string?> member3 = new Dictionary<string, string?>
        {
            { "Plan ID", "PLAN-003" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "Bob" },
            { "Last Name", "Johnson" },
            { "Name", "Bob Johnson" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-35).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "C123456(9)" },
            { "Staff Number", "STAFF003" }
        };

        return [member1, member2, member3];
    }

    private List<IReadOnlyDictionary<string, string?>> CreateMemberDataWithMemberIds()
    {
        Dictionary<string, string?> member1 = new Dictionary<string, string?>
        {
            { "Member ID", "MEMBER-001" },
            { "Plan ID", "PLAN-001" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "John" },
            { "Last Name", "Doe" },
            { "Name", "John Doe" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-30).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "A123456(7)" },
            { "Staff Number", "STAFF001" }
        };

        Dictionary<string, string?> member2 = new Dictionary<string, string?>
        {
            { "Member ID", "MEMBER-002" },
            { "Plan ID", "PLAN-002" },
            { "Effective Date", DateTime.Today.ToString("yyyy-MM-dd") },
            { "First Name", "Jane" },
            { "Last Name", "Smith" },
            { "Name", "Jane Smith" },
            { "Member Type", "employee" },
            { "Date of Birth", DateTime.Today.AddYears(-25).ToString("yyyy-MM-dd") },
            { "Email", "<EMAIL>" },
            { "HKID", "B123456(8)" },
            { "Staff Number", "STAFF002" }
        };

        return [member1, member2];
    }

    private List<PolicyDto> CreateContractHolderPolicies() =>
    [
        MemberUploadTestDataBuilder.Create().WithPolicyId("POLICY-001").BuildPolicyDto(),
        MemberUploadTestDataBuilder.Create().WithPolicyId("POLICY-002").BuildPolicyDto(),
        MemberUploadTestDataBuilder.Create().WithPolicyId("POLICY-003").BuildPolicyDto()
    ];

    private Individual CreateMockIndividual(string internalCode) => new()
    {
        InternalCode = internalCode,
        EnglishFirstName = "Test",
        EnglishLastName = "User"
    };

    private void SetupAllScopeValidationServices()
    {
        // Setup tenant scope validation
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup policy scope validation
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup contract holder scope validation
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup contract holder policies with valid GUIDs
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([Guid.NewGuid().ToString(), Guid.NewGuid().ToString()]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateContractHolderPolicies());

        // Setup users service for user scope validation
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([CreateMockIndividual("MEMBER-001"), CreateMockIndividual("MEMBER-002")]);
    }

    private void SetupMixedScopeValidationResults()
    {
        // Setup tenant scope validation to return some conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["email"]);

        // Setup policy scope validation to pass
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup contract holder scope validation to return conflicts
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(["hkid"]);

        // Setup contract holder policies
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["POLICY-001", "POLICY-002"]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateContractHolderPolicies());

        // Setup users service to return partial results (some users missing)
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([CreateMockIndividual("MEMBER-001")]); // MEMBER-002 is missing
    }

    private void VerifyAllScopeValidationInteractions()
    {
        // Verify tenant scope validation was called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateTenantScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);

        // Verify policy scope validation was called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidatePolicyScopeUniquenessAsync(
            It.IsAny<PolicyId>(),
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);

        // Verify contract holder scope validation was called
        _mockPolicyMemberUniquenessService.Verify(x => x.ValidateContractHolderScopeUniquenessAsync(
            It.IsAny<string?>(),
            It.IsAny<PolicyMemberId?>(),
            It.IsAny<List<PolicyId>>(),
            It.IsAny<List<EndorsementId>>(),
            It.IsAny<Dictionary<string, object>>(),
            It.IsAny<List<string>>(),
            It.IsAny<CancellationToken>()), Times.AtLeastOnce);

        // Verify users service was called for user scope validation
        _mockUsersService.Verify(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()), Times.Once);

        // Verify contract holder policy retrieval
        _mockLegacyPolicyService.Verify(x => x.GetIdsByContractHolderId(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    private void SetupValidationScenario(
        IReadOnlyList<IReadOnlyDictionary<string, string?>> memberData,
        PolicyDto? policy = null,
        PolicyMemberFieldsSchema? schema = null,
        ResolvedValidationData? resolvedData = null)
    {
        // Setup default mocks first
        SetupDefaultMocks();

        PolicyMemberUpload upload = CreateValidUpload();
        PolicyDto testPolicy = policy ?? CreateValidPolicy();
        PolicyMemberFieldsSchema testSchema = schema ?? CreateTestSchema();
        _ = resolvedData ?? ResolvedValidationDataTestDataBuilder.Create();

        _mockUploadRepository.Setup(x => x.FindByIdAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(upload);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyById(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testPolicy);

        _mockSchemaProvider.Setup(x => x.GetMemberUploadSchema(It.IsAny<string?>(), It.IsAny<ProductId>(), It.IsAny<EndorsementId?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(testSchema);

        // Setup file processing service to return test member data
        _mockFileProcessingService.Setup(x => x.ProcessUploadFileAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(FileProcessingResult.Success(memberData, memberData.Count));

        // If your validation context is constructed via a service or factory, mock it here to return the context with testResolvedData.
        // (If not, ensure downstream code can access testResolvedData as required.)
    }

    private void SetupDefaultMocks()
    {
        // Setup memory cache mock
        var mockCacheEntry = new Mock<ICacheEntry>();
        _mockMemoryCache.Setup(x => x.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);
        _mockMemoryCache.Setup(x => x.TryGetValue(It.IsAny<object>(), out It.Ref<object?>.IsAny)).Returns(false);

        // Setup default feature flags
        _mockFeatureManager.Setup(x => x.IsEnabled("UseTheSamePlanForEmployeeAndDependents", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("OnlyApplyUseTheSamePlanForEmployeeAndDependentsForSmeProducts", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("AllowMembersFromOtherContractHolders", _tenantId.Value))
            .ReturnsAsync(false);
        _mockFeatureManager.Setup(x => x.IsEnabled("SkipContractHolderUniqueRulesWhenAddPolicyMember", _tenantId.Value))
            .ReturnsAsync(false);

        // Setup default product service responses
        _mockProductService.Setup(x => x.GetAvailablePlanIds(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(["PLAN-001", "PLAN-002", "PLAN-003"]);

        _mockProductService.Setup(x => x.GetProductPackageType(It.IsAny<Products.Client.ProductId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("sme");

        // Setup default policy service responses
        _mockLegacyPolicyService.Setup(x => x.GetIdsByContractHolderId(It.IsAny<string?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockLegacyPolicyService.Setup(x => x.GetPolicyDtosByIds(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default users service responses
        _mockUsersService.Setup(x => x.QueryIndividuals(It.IsAny<QueryArgumentsOfIndividualWhere>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default member query service responses
        _mockPolicyMemberQueryService.Setup(x => x.GetPolicyMembersBatchAsync(It.IsAny<List<string>>(), It.IsAny<PolicyId>(), It.IsAny<List<EndorsementId>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberQueryService.Setup(x => x.GetMemberValidationStatesBatchAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup default uniqueness service responses (no conflicts)
        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateTenantScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidatePolicyScopeUniquenessAsync(
                It.IsAny<PolicyId>(),
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        _mockPolicyMemberUniquenessService.Setup(x => x.ValidateContractHolderScopeUniquenessAsync(
                It.IsAny<string?>(),
                It.IsAny<PolicyMemberId?>(),
                It.IsAny<List<PolicyId>>(),
                It.IsAny<List<EndorsementId>>(),
                It.IsAny<Dictionary<string, object>>(),
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync([]);

        // Setup repository operations
        _mockUploadRepository.Setup(x => x.StartValidationIfNotLockedAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        _mockUploadRepository.Setup(x => x.CompleteValidationIfNotLockedAsync(
                It.IsAny<PolicyMemberUploadId>(),
                It.IsAny<PolicyMemberUploadStatus>(),
                It.IsAny<int>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);
    }

    #endregion
}