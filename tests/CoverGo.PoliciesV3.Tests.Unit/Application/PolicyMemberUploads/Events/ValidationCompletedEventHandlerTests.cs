using CoverGo.PoliciesV3.Application.PolicyMemberUploads.Events;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Events;
using Microsoft.Extensions.Logging;
using Moq;

namespace CoverGo.PoliciesV3.Tests.Unit.Application.PolicyMemberUploads.Events;

public class ValidationCompletedEventHandlerTests
{
    private readonly Mock<IValidationErrorManagerService> _mockValidationErrorManager;
    private readonly Mock<ILogger<ValidationCompletedEventHandler>> _mockLogger;
    private readonly ValidationCompletedEventHandler _handler;

    public ValidationCompletedEventHandlerTests()
    {
        _mockValidationErrorManager = new Mock<IValidationErrorManagerService>();
        _mockLogger = new Mock<ILogger<ValidationCompletedEventHandler>>();
        _handler = new ValidationCompletedEventHandler(_mockValidationErrorManager.Object, _mockLogger.Object);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task Handle_WithValidEvent_ShouldClearAndPersistValidationErrors()
    {
        // Arrange
        var uploadId = PolicyMemberUploadId.New;
        var memberErrors = new Dictionary<int, List<ValidationError>>
        {
            { 0, new List<ValidationError> { Errors.Required("Name", "Name") } },
            { 1, new List<ValidationError> { Errors.InvalidFormat("Email", "Email") } }
        };
        var validationEvent = new ValidationCompletedEvent(uploadId, 5, 2, memberErrors);
        var cancellationToken = CancellationToken.None;

        // Act
        await _handler.Handle(validationEvent, cancellationToken);

        // Assert
        _mockValidationErrorManager.Verify(
            x => x.ClearPreviousValidationErrorsAsync(uploadId, cancellationToken),
            Times.Once);

        _mockValidationErrorManager.Verify(
            x => x.PersistValidationErrorsAsync(uploadId, memberErrors, cancellationToken),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task Handle_WithEmptyErrors_ShouldStillClearAndPersist()
    {
        // Arrange
        var uploadId = PolicyMemberUploadId.New;
        var memberErrors = new Dictionary<int, List<ValidationError>>();
        var validationEvent = new ValidationCompletedEvent(uploadId, 10, 0, memberErrors);
        var cancellationToken = CancellationToken.None;

        // Act
        await _handler.Handle(validationEvent, cancellationToken);

        // Assert
        _mockValidationErrorManager.Verify(
            x => x.ClearPreviousValidationErrorsAsync(uploadId, cancellationToken),
            Times.Once);

        _mockValidationErrorManager.Verify(
            x => x.PersistValidationErrorsAsync(uploadId, memberErrors, cancellationToken),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task Handle_WhenClearThrowsException_ShouldPropagateException()
    {
        // Arrange
        var uploadId = PolicyMemberUploadId.New;
        var memberErrors = new Dictionary<int, List<ValidationError>>();
        var validationEvent = new ValidationCompletedEvent(uploadId, 5, 0, memberErrors);
        var cancellationToken = CancellationToken.None;
        var expectedException = new InvalidOperationException("Clear failed");

        _mockValidationErrorManager
            .Setup(x => x.ClearPreviousValidationErrorsAsync(uploadId, cancellationToken))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(validationEvent, cancellationToken));

        Assert.Equal(expectedException.Message, exception.Message);

        // Verify persist was not called due to exception
        _mockValidationErrorManager.Verify(
            x => x.PersistValidationErrorsAsync(It.IsAny<PolicyMemberUploadId>(), It.IsAny<Dictionary<int, List<ValidationError>>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task Handle_WhenPersistThrowsException_ShouldPropagateException()
    {
        // Arrange
        var uploadId = PolicyMemberUploadId.New;
        var memberErrors = new Dictionary<int, List<ValidationError>>
        {
            { 0, new List<ValidationError> { Errors.Required("Name", "Name") } }
        };
        var validationEvent = new ValidationCompletedEvent(uploadId, 5, 1, memberErrors);
        var cancellationToken = CancellationToken.None;
        var expectedException = new InvalidOperationException("Persist failed");

        _mockValidationErrorManager
            .Setup(x => x.PersistValidationErrorsAsync(uploadId, memberErrors, cancellationToken))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _handler.Handle(validationEvent, cancellationToken));

        Assert.Equal(expectedException.Message, exception.Message);

        // Verify clear was called before the exception
        _mockValidationErrorManager.Verify(
            x => x.ClearPreviousValidationErrorsAsync(uploadId, cancellationToken),
            Times.Once);
    }
}
