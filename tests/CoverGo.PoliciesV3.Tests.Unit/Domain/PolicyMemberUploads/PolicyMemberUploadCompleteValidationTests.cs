using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Events;
using CoverGo.PoliciesV3.Domain.ValueObjects;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.PolicyMemberUploads;

public class PolicyMemberUploadCompleteValidationTests
{
    [Fact]
    [Trait("Category", "Unit")]
    public void CompleteValidation_WithValidResults_ShouldSetCountsAndRaiseDomainEvent()
    {
        // Arrange
        var policyId = PolicyId.New;
        var upload = PolicyMemberUpload.Create(policyId, "/test/path.csv", 10);

        var memberErrors = new Dictionary<int, List<ValidationError>>
        {
            { 0, new List<ValidationError> { Errors.Required("Name", "Name") } },
            { 2, new List<ValidationError> { Errors.InvalidFormat("Email", "Email") } }
        };

        var results = ValidationOrchestrationResult.Success(8, 2, memberErrors);

        // Act
        upload.CompleteValidation(results);

        // Assert
        upload.ValidMembersCount.Should().Be(8);
        upload.InvalidMembersCount.Should().Be(2);
        upload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATING_ERROR);

        // Verify domain event was raised
        upload.DomainEvents.Should().HaveCount(1);
        var domainEvent = upload.DomainEvents.First().Should().BeOfType<ValidationCompletedEvent>().Subject;
        domainEvent.AggregateId.Should().Be(upload.Id);
        domainEvent.ValidCount.Should().Be(8);
        domainEvent.InvalidCount.Should().Be(2);
        domainEvent.MemberErrors.Should().BeEquivalentTo(memberErrors);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public void CompleteValidation_WithNoErrors_ShouldSetStatusToValidated()
    {
        // Arrange
        var policyId = PolicyId.New;
        var upload = PolicyMemberUpload.Create(policyId, "/test/path.csv", 5);

        var memberErrors = new Dictionary<int, List<ValidationError>>();
        var results = ValidationOrchestrationResult.Success(5, 0, memberErrors);

        // Act
        upload.CompleteValidation(results);

        // Assert
        upload.ValidMembersCount.Should().Be(5);
        upload.InvalidMembersCount.Should().Be(0);
        upload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATED);

        // Verify domain event was raised
        upload.DomainEvents.Should().HaveCount(1);
        var domainEvent = upload.DomainEvents.First().Should().BeOfType<ValidationCompletedEvent>().Subject;
        domainEvent.AggregateId.Should().Be(upload.Id);
        domainEvent.ValidCount.Should().Be(5);
        domainEvent.InvalidCount.Should().Be(0);
        domainEvent.MemberErrors.Should().BeEmpty();
    }

    [Fact]
    [Trait("Category", "Unit")]
    public void CompleteValidation_WithPartialValidation_ShouldNotChangeStatus()
    {
        // Arrange
        var policyId = PolicyId.New;
        var upload = PolicyMemberUpload.Create(policyId, "/test/path.csv", 10);

        var memberErrors = new Dictionary<int, List<ValidationError>>
        {
            { 0, new List<ValidationError> { Errors.Required("Name", "Name") } }
        };

        // Only 5 members processed out of 10
        var results = ValidationOrchestrationResult.Success(4, 1, memberErrors);

        // Act
        upload.CompleteValidation(results);

        // Assert
        upload.ValidMembersCount.Should().Be(4);
        upload.InvalidMembersCount.Should().Be(1);
        upload.Status.Should().Be(PolicyMemberUploadStatus.REGISTERED); // Should remain unchanged

        // Verify domain event was still raised
        upload.DomainEvents.Should().HaveCount(1);
        var domainEvent = upload.DomainEvents.First().Should().BeOfType<ValidationCompletedEvent>().Subject;
        domainEvent.AggregateId.Should().Be(upload.Id);
        domainEvent.ValidCount.Should().Be(4);
        domainEvent.InvalidCount.Should().Be(1);
        domainEvent.MemberErrors.Should().BeEquivalentTo(memberErrors);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public void CompleteValidation_CalledMultipleTimes_ShouldRaiseMultipleEvents()
    {
        // Arrange
        var policyId = PolicyId.New;
        var upload = PolicyMemberUpload.Create(policyId, "/test/path.csv", 5);

        var firstResults = ValidationOrchestrationResult.Success(3, 2, new Dictionary<int, List<ValidationError>>());
        var secondResults = ValidationOrchestrationResult.Success(5, 0, new Dictionary<int, List<ValidationError>>());

        // Act
        upload.CompleteValidation(firstResults);
        upload.CompleteValidation(secondResults);

        // Assert
        upload.ValidMembersCount.Should().Be(5);
        upload.InvalidMembersCount.Should().Be(0);
        upload.Status.Should().Be(PolicyMemberUploadStatus.VALIDATED);

        // Verify both domain events were raised
        upload.DomainEvents.Should().HaveCount(2);

        var firstEvent = upload.DomainEvents.First().Should().BeOfType<ValidationCompletedEvent>().Subject;
        firstEvent.ValidCount.Should().Be(3);
        firstEvent.InvalidCount.Should().Be(2);

        var secondEvent = upload.DomainEvents.Last().Should().BeOfType<ValidationCompletedEvent>().Subject;
        secondEvent.ValidCount.Should().Be(5);
        secondEvent.InvalidCount.Should().Be(0);
    }
}
